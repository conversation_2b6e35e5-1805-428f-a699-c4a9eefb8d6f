import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, ArrowRight, Package, DollarSign, Tag } from "lucide-react";
import { useNavigate } from 'react-router-dom';
import { getEnhancedCategories } from '@/lib/api';
import { Badge } from "@/components/ui/badge";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";

const Categories = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      setError(null);

      try {
        const categoriesData = await getEnhancedCategories();
        setCategories(categoriesData);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Filter categories based on search query
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Navigate to products page with category filter
  const navigateToCategory = (categoryId: number, categoryName: string) => {
    navigate(`/products?category=${categoryName.toLowerCase()}`);
  };

  // Calculate total products
  const totalProducts = categories.reduce((sum, category) => sum + category.productCount, 0);

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">Categories</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Explore product categories to find items to promote
        </p>
      </div>

      {/* Summary cards */}
      {!loading && !error && categories.length > 0 && (
        <div className="grid grid-cols-2 gap-4 mb-6">
          <Card className="border shadow-sm">
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Categories</p>
                <p className="text-2xl font-bold">{categories.length}</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <Tag size={20} className="text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card className="border shadow-sm">
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Products</p>
                <p className="text-2xl font-bold">{totalProducts}</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <Package size={20} className="text-primary" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search bar */}
      <div className="relative max-w-md w-full mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" size={18} />
        <Input
          placeholder="Search categories..."
          className="pl-10 w-full bg-muted/40"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Section title */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Browse Categories</h2>
        <p className="text-sm text-muted-foreground">
          {filteredCategories.length} {filteredCategories.length === 1 ? 'category' : 'categories'} available
        </p>
      </div>

      {/* Categories grid */}
      {loading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5 md:gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="h-[280px] animate-pulse">
              <div className="h-full flex flex-col">
                <div className="aspect-square w-full bg-muted rounded-t-lg"></div>
                <div className="p-4 flex-1 flex flex-col gap-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="mt-auto h-8 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
        </div>
      ) : filteredCategories.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">No categories found matching your search.</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5 md:gap-6">
          <TooltipProvider>
            {filteredCategories.map((category) => (
              <Tooltip key={category.id}>
                <TooltipTrigger asChild>
                  <Card
                    className="overflow-hidden transition-all duration-300 hover:shadow-lg group cursor-pointer border h-full flex flex-col"
                    onClick={() => navigateToCategory(category.id, category.name)}
                  >
                    <div className="relative aspect-square w-full overflow-hidden">
                      {/* Shimmer effect for loading */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-200/20 to-transparent animate-shimmer bg-[length:200%_100%]"></div>

                      {/* Category image */}
                      <div className="absolute inset-0 z-10 p-4">
                        <img
                          src={category.image}
                          alt={category.name}
                          className="w-full h-full object-contain rounded-lg transition-transform duration-700 group-hover:scale-105"
                          onError={(e) => {
                            e.currentTarget.src = `https://picsum.photos/seed/${category.name}/400/400`;
                          }}
                        />
                      </div>

                      {/* Category badge */}
                      <div className="absolute top-3 right-3 z-20">
                        <Badge className="bg-primary/90 hover:bg-primary text-white font-medium px-2 py-1">
                          <Tag size={12} className="mr-1" />
                          {category.name}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-4 flex-1">
                      <h3 className="font-bold text-base sm:text-lg mb-2">{category.name}</h3>

                      <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                        <Package size={14} />
                        <span>{category.productCount} Products</span>
                      </div>
                    </CardContent>

                    <CardFooter className="p-3 border-t mt-auto">
                      <Button
                        variant="default"
                        size="sm"
                        className="w-full"
                      >
                        <span className="mr-1">View Products</span>
                        <ArrowRight size={14} />
                      </Button>
                    </CardFooter>
                  </Card>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>View {category.productCount} products in {category.name}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
        </div>
      )}
    </Layout>
  );
};

export default Categories;
