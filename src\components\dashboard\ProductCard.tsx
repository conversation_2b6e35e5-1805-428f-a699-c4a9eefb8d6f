import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Link, Copy, Eye, Heart, ArrowRight } from "lucide-react";
import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { formatCurrencyDa } from '@/lib/utils';
import { useFavorites } from '@/contexts/FavoritesContext';

interface ProductCardProps {
  product: {
    id: number | string;
    title: string;
    name?: string; // For backward compatibility
    image?: string;
    price: number;
    commission: number;
    description: string;
    category: string;
    rating?: {
      rate: number;
      count: number;
    };
    tags?: string[];
    stockCount?: number;
    inStock?: boolean;
  };
}

const ProductCard = ({ product }: ProductCardProps) => {
  const navigate = useNavigate();
  const { isFavorited, addToFavorites, removeFromFavorites } = useFavorites();
  const commissionAmount = (product.price * product.commission) / 100;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if this product is favorited
  const isFavorite = isFavorited(product.id.toString());

  // Check if the device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Use the product image or a fallback
  const imageUrl = product.image;

  // Fallback image in case the main one fails
  const fallbackImage = `https://picsum.photos/seed/${product.id}/600/400`;

  // Get product name (handle API data format)
  const productName = product.title || product.name;

  const copyAffiliateLink = (e?: React.MouseEvent) => {
    // Prevent event bubbling if called from a button click
    e?.stopPropagation();

    // In a real application, this would generate a unique affiliate link
    const affiliateLink = `https://example.com/products/${product.id}?ref=user123`;
    navigator.clipboard.writeText(affiliateLink);
    toast.success("Affiliate link copied to clipboard!");
  };

  const addBuyer = (e?: React.MouseEvent) => {
    // Prevent event bubbling if called from a button click
    e?.stopPropagation();

    // Navigate to product detail page with buy tab active
    navigate(`/products/${product.id}?tab=buy`);
  };

  const goToProductDetail = () => {
    navigate(`/products/${product.id}`);
  };

  const toggleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event

    if (isFavorite) {
      await removeFromFavorites(product.id.toString());
    } else {
      await addToFavorites(product);
    }
  };


  return (
    <>
      <Card
        className="overflow-hidden transition-all duration-300 h-full flex flex-col hover:shadow-lg group relative cursor-pointer"
        onClick={goToProductDetail}
      >
        <CardHeader className="p-0">
          <div className="relative aspect-square w-full overflow-hidden border border-gray-100">
            {/* Shimmer loading effect */}
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            )}

            {/* Loading spinner */}
            {!imageLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-3 border-primary/30 border-t-primary rounded-full animate-spin"></div>
              </div>
            )}

            {/* Product image with container */}
            <div className="absolute inset-0 flex items-center justify-center p-2">
              <img
                src={imageUrl}
                alt={productName}
                className={`max-w-full max-h-full object-contain transition-all duration-500 rounded-md ${
                  imageLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
                }`}
                onLoad={() => setImageLoaded(true)}
                onError={(e) => {
                  e.currentTarget.src = fallbackImage;
                  setImageLoaded(true);
                }}
              />
            </div>

            {/* Overlay on hover */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 touch-none"></div>

            {/* Commission badge hidden as requested */}

            {/* View button on hover */}
            <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
              <Button variant="secondary" size="icon" className="bg-white/90 hover:bg-white shadow-sm">
                <Eye size={16} />
              </Button>
            </div>
          </div>
        </CardHeader>
      <CardContent className="pt-4 pb-2 sm:pt-5 sm:pb-3 flex-1 space-y-3">
        <h3 className="font-bold text-base sm:text-lg line-clamp-2 group-hover:text-primary transition-colors mb-2">
          {productName}
        </h3>
        <div className="flex justify-between items-center">
          <div>
            <p className="text-lg sm:text-xl font-bold">{formatCurrencyDa(product.price)}</p>
            <p className="text-xs sm:text-sm text-emerald-600 font-semibold">
              Earn: {formatCurrencyDa(commissionAmount)}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Badge
            variant="outline"
            className={`text-xs px-2 py-0.5 border-gray-200 ${(product.stockCount || 0) > 0 ? 'text-green-600' : 'text-red-500'}`}
          >
            {(product.stockCount || 0) > 0 ? `${product.stockCount} in stock` : "Out of stock"}
          </Badge>
        </div>
        <div className="flex flex-wrap gap-1 sm:gap-1.5 mt-3 sm:mt-4">
          <span
            className="bg-secondary/70 hover:bg-secondary text-secondary-foreground text-[10px] sm:text-xs px-2 py-0.5 sm:px-2.5 sm:py-1 rounded-full transition-colors"
          >
            {product.category}
          </span>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2 border-t p-2 sm:p-3 justify-between">
        <Button
          variant="default"
          size="sm"
          className="flex-1 h-8 text-xs sm:text-sm"
          onClick={goToProductDetail}
        >
          <span className="whitespace-nowrap mr-1 sm:mr-2">View Details</span>
          <ArrowRight size={14} />
        </Button>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={isFavorite ? "default" : "outline"}
                size="icon"
                className={`h-8 w-8 ${isFavorite ? "bg-red-500 hover:bg-red-600 border-red-500" : ""}`}
                onClick={toggleFavorite}
              >
                <Heart size={16} className={isFavorite ? "text-white fill-white" : ""} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isFavorite ? "Remove from favorites" : "Add to favorites"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardFooter>
      </Card>
    </>
  );
};

export default ProductCard;
