-- Supabase Database Schema for Admin Panel
-- Run this script in your Supabase SQL editor to create all necessary tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Profiles table for storing additional user information
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'admin' CHECK (role IN ('admin', 'manager', 'affiliate')),
    phone VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for profiles table
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    affiliate_earning_price DECIMAL(10,2) NOT NULL,
    category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    main_image TEXT NOT NULL,
    gallery_images TEXT[],
    in_stock BOOLEAN DEFAULT true,
    stock_count INTEGER DEFAULT 0,
    free_shipping BOOLEAN DEFAULT false,
    shipping_cost DECIMAL(10,2) DEFAULT 0,
    rating_rate DECIMAL(3,2) DEFAULT 0,
    rating_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    location VARCHAR(255) NOT NULL,
    address TEXT,
    join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Affiliate workers table
CREATE TABLE IF NOT EXISTS affiliate_workers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(50) NOT NULL,
    location VARCHAR(255) NOT NULL,
    commission_rate DECIMAL(5,2) DEFAULT 10.0,
    total_earnings DECIMAL(10,2) DEFAULT 0,
    join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    affiliate_worker_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    order_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    total DECIMAL(10,2) NOT NULL,
    items_count INTEGER NOT NULL,
    payment_method VARCHAR(100) NOT NULL,
    shipping_cost DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    affiliate_earnings DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Delivery locations table (58 Algerian wilayas)
CREATE TABLE IF NOT EXISTS delivery_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    wilaya_code INTEGER NOT NULL UNIQUE,
    wilaya_name VARCHAR(255) NOT NULL,
    desk_price DECIMAL(10,2) NOT NULL,
    domicile_price DECIMAL(10,2) NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    affiliate_worker_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'cancelled')),
    payment_method VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User favorites table (many-to-many relationship between users and products)
CREATE TABLE IF NOT EXISTS user_favorites (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Enable RLS on user_favorites table
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;

-- Create policies for user_favorites table
CREATE POLICY "Users can view own favorites" ON user_favorites FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own favorites" ON user_favorites FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own favorites" ON user_favorites FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_affiliate_worker_id ON orders(affiliate_worker_id);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_payments_affiliate_worker_id ON payments(affiliate_worker_id);
CREATE INDEX IF NOT EXISTS idx_delivery_locations_wilaya_code ON delivery_locations(wilaya_code);
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_product_id ON user_favorites(product_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_product ON user_favorites(user_id, product_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_affiliate_workers_updated_at BEFORE UPDATE ON affiliate_workers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_delivery_locations_updated_at BEFORE UPDATE ON delivery_locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample categories
INSERT INTO categories (name, image) VALUES
('Electronics', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?q=80&w=500&fit=crop'),
('Clothing', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=500&fit=crop'),
('Home & Garden', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=500&fit=crop'),
('Sports & Fitness', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=500&fit=crop'),
('Beauty & Health', 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?q=80&w=500&fit=crop')
ON CONFLICT (name) DO NOTHING;

-- Insert Algerian wilayas for delivery locations
INSERT INTO delivery_locations (wilaya_code, wilaya_name, desk_price, domicile_price) VALUES
(1, 'Adrar', 500, 600),
(2, 'Chlef', 400, 500),
(3, 'Laghouat', 450, 550),
(4, 'Oum El Bouaghi', 400, 500),
(5, 'Batna', 400, 500),
(6, 'Béjaïa', 350, 450),
(7, 'Biskra', 450, 550),
(8, 'Béchar', 550, 650),
(9, 'Blida', 300, 400),
(10, 'Bouira', 350, 450),
(11, 'Tamanrasset', 700, 800),
(12, 'Tébessa', 450, 550),
(13, 'Tlemcen', 400, 500),
(14, 'Tiaret', 400, 500),
(15, 'Tizi Ouzou', 350, 450),
(16, 'Alger', 250, 350),
(17, 'Djelfa', 400, 500),
(18, 'Jijel', 400, 500),
(19, 'Sétif', 400, 500),
(20, 'Saïda', 450, 550),
(21, 'Skikda', 400, 500),
(22, 'Sidi Bel Abbès', 400, 500),
(23, 'Annaba', 450, 550),
(24, 'Guelma', 400, 500),
(25, 'Constantine', 400, 500),
(26, 'Médéa', 350, 450),
(27, 'Mostaganem', 400, 500),
(28, 'MSila', 400, 500),
(29, 'Mascara', 400, 500),
(30, 'Ouargla', 500, 600),
(31, 'Oran', 350, 450),
(32, 'El Bayadh', 450, 550),
(33, 'Illizi', 650, 750),
(34, 'Bordj Bou Arréridj', 400, 500),
(35, 'Boumerdès', 300, 400),
(36, 'El Tarf', 450, 550),
(37, 'Tindouf', 600, 700),
(38, 'Tissemsilt', 400, 500),
(39, 'El Oued', 500, 600),
(40, 'Khenchela', 450, 550),
(41, 'Souk Ahras', 450, 550),
(42, 'Tipaza', 300, 400),
(43, 'Mila', 400, 500),
(44, 'Aïn Defla', 350, 450),
(45, 'Naâma', 500, 600),
(46, 'Aïn Témouchent', 400, 500),
(47, 'Ghardaïa', 500, 600),
(48, 'Relizane', 400, 500),
(49, 'Timimoun', 550, 650),
(50, 'Bordj Badji Mokhtar', 650, 750),
(51, 'Ouled Djellal', 450, 550),
(52, 'Béni Abbès', 600, 700),
(53, 'In Salah', 650, 750),
(54, 'In Guezzam', 700, 800),
(55, 'Touggourt', 500, 600),
(56, 'Djanet', 700, 800),
(57, 'El MGhair', 500, 600),
(58, 'El Meniaa', 550, 650)
ON CONFLICT (wilaya_code) DO NOTHING;
