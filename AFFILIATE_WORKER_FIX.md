# Affiliate Worker Foreign Key Fix

## Problem
The `orders` and `payments` tables had foreign key references to the `affiliate_workers` table, but this table was empty and not being used. The actual users/workers are stored in the `auth.users` table (accessed through the `profiles` table).

## Solution Implemented

### 1. Database Schema Updates
- **File**: `database-fix-affiliate-worker-fk.sql`
- **Action**: Run this script in your Supabase SQL editor to fix the foreign key constraints
- **Changes**:
  - Drop existing foreign key constraints that reference `affiliate_workers`
  - Add new foreign key constraints that reference `auth.users`
  - Update indexes for better performance
  - Add comments for clarity

### 2. API Functions Updated
- **File**: `src/lib/api-extended.ts`
- **Changes**:
  - Updated `fetchOrders()` to fetch user profiles separately (avoids foreign key relationship issues)
  - Updated `fetchOrderById()` to fetch user profile separately when affiliate_worker_id exists
  - Updated `fetchPayments()` to fetch user profiles separately
  - Replaced `fetchAffiliateWorkers()` with `fetchAffiliateUsers()` (uses profiles table)
  - Replaced `fetchAffiliateWorkerById()` with `fetchUserProfileById()` (uses profiles table)
  - **Note**: Uses separate queries instead of joins to avoid Supabase foreign key relationship detection issues

### 3. Type Definitions Updated
- **File**: `types.ts`
- **Changes**:
  - Simplified `AffiliateWorker` interface to match user profile structure
  - Removed unused fields that were specific to the old affiliate_workers table

### 4. AddBuyerDialog Component
- **File**: `src/components/product/AddBuyerDialog.tsx`
- **Changes**:
  - Now correctly uses `user.id` from auth context for `affiliate_worker_id`
  - Validates user authentication before order submission
  - Properly handles affiliate earnings calculation

## Database Migration Required

**IMPORTANT**: You must run the SQL script to fix the database schema:

1. Open your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `database-fix-affiliate-worker-fk.sql`
4. Execute the script

## What This Fixes

1. **Foreign Key Errors**: Orders can now be created without foreign key constraint violations
2. **Data Consistency**: Orders are properly linked to actual users instead of empty affiliate_workers table
3. **Authentication Integration**: Orders are correctly associated with the logged-in user
4. **API Consistency**: All API functions now use the correct user/profile relationships

## Testing

After running the database migration:

1. Test order creation through the AddBuyerDialog
2. Verify that orders are properly linked to the authenticated user
3. Check that order fetching works correctly with user profile data
4. Ensure payments functionality works with the new foreign key structure

## Backward Compatibility

- The old `affiliate_workers` table is preserved but not used
- API function names have been updated but old functions are commented for reference
- Type definitions maintain compatibility with existing code

## Next Steps

1. Run the database migration script
2. Test the order creation functionality
3. Update any other parts of the application that might reference the old affiliate_workers table
4. Consider removing the unused `affiliate_workers` table in a future cleanup
