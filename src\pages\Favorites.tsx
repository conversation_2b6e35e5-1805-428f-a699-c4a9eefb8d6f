import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { Heart, Search, Filter, Trash2, ShoppingBag, ArrowUpDown } from 'lucide-react';
import { toast } from 'sonner';
import ProductCard from '@/components/dashboard/ProductCard';
import { useIsMobile } from '@/hooks/use-mobile';
import { useFavorites } from '@/contexts/FavoritesContext';
import type { Product } from '@/types';

const Favorites = () => {
  const { favorites, loading, removeFromFavorites } = useFavorites();
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('newest');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const isMobile = useIsMobile();

  // Filter and sort products when search term, sort option, or category filter changes
  useEffect(() => {
    let result = [...favorites];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(product =>
        product.title.toLowerCase().includes(term) ||
        product.category.toLowerCase().includes(term)
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      result = result.filter(product => product.category === categoryFilter);
    }

    // Apply sorting
    switch (sortOption) {
      case 'priceAsc':
        result.sort((a, b) => a.price - b.price);
        break;
      case 'priceDesc':
        result.sort((a, b) => b.price - a.price);
        break;
      case 'nameAsc':
        result.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'nameDesc':
        result.sort((a, b) => b.title.localeCompare(a.title));
        break;
      case 'newest':
        // Sort by date added to favorites (favorited_at field)
        result.sort((a, b) => {
          const dateA = new Date(a.favorited_at || a.created_at).getTime();
          const dateB = new Date(b.favorited_at || b.created_at).getTime();
          return dateB - dateA;
        });
        break;
      default:
        break;
    }

    setFilteredProducts(result);
  }, [searchTerm, sortOption, categoryFilter, favorites]);

  // Get unique categories from favorites
  const categories = ['all', ...new Set(favorites.map(product => product.category))];

  // Handle remove from favorites
  const handleRemoveFromFavorites = async (productId: string) => {
    await removeFromFavorites(productId);
  };

  // Clear all favorites
  const clearAllFavorites = async () => {
    // Remove all favorites one by one
    const removePromises = favorites.map(product => removeFromFavorites(product.id.toString()));
    await Promise.all(removePromises);
    toast.success('All favorites cleared');
  };

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">My Favorites</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Products you've saved for later
        </p>
      </div>

      <Card className="p-4 sm:p-6 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search favorites..."
              className="pl-9 w-full sm:w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 w-full sm:w-auto">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category, index) => (
                  <SelectItem key={index} value={category}>
                    {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={sortOption} onValueChange={setSortOption}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <ArrowUpDown className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="priceAsc">Price: Low to High</SelectItem>
                <SelectItem value="priceDesc">Price: High to Low</SelectItem>
                <SelectItem value="nameAsc">Name: A to Z</SelectItem>
                <SelectItem value="nameDesc">Name: Z to A</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              className="w-full sm:w-auto"
              onClick={clearAllFavorites}
              disabled={favorites.length === 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </div>
      </Card>

      {loading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="h-[300px] animate-pulse">
              <div className="h-full flex flex-col">
                <div className="h-[180px] bg-muted rounded-t-lg"></div>
                <div className="p-4 flex-1 flex flex-col gap-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="mt-auto h-8 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : filteredProducts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <div key={product.id} className="relative group">
              <ProductCard product={product} />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => handleRemoveFromFavorites(product.id.toString())}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <Heart className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No favorites yet</h3>
          <p className="text-muted-foreground mb-6">
            Products you favorite will appear here for easy access
          </p>
          <Button onClick={() => window.location.href = '/products'}>
            <ShoppingBag className="h-4 w-4 mr-2" />
            Browse Products
          </Button>
        </div>
      )}
    </Layout>
  );
};

export default Favorites;
