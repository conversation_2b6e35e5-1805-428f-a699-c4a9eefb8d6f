
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 260 20% 99%;
    --foreground: 240 10% 4%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 4%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 260 94% 96%;
    --secondary-foreground: 240 6% 10%;

    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;

    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 0 0% 0%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 10% 4%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 240 6% 90%;
    --sidebar-ring: 0 0% 0%;
  }

  .dark {
    --background: 240 10% 4%;
    --foreground: 0 0% 98%;

    --card: 240 10% 4%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 4%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 4% 16%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 0 0% 0%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 0 0% 0%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  .glass-card {
    @apply bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-sm;
  }

  .stats-card {
    @apply p-6 rounded-xl flex flex-col gap-2 transition-all duration-300 hover:shadow-md;
  }

  .animate-delay-1 {
    animation-delay: 0.1s;
  }

  .animate-delay-2 {
    animation-delay: 0.2s;
  }

  .animate-delay-3 {
    animation-delay: 0.3s;
  }

  .animate-delay-4 {
    animation-delay: 0.4s;
  }

  /* Sidebar styles */
  .bg-sidebar {
    @apply bg-background dark:bg-background;
  }

  .text-sidebar-foreground {
    @apply text-foreground dark:text-foreground;
  }

  .bg-sidebar-accent {
    @apply bg-primary/10 dark:bg-primary/20;
  }

  .text-sidebar-accent-foreground {
    @apply text-primary dark:text-primary;
  }
}
