
import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { formatDate, formatCurrencyDa } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Search,
  Filter,
  Eye,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Package,
  Loader2,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/contexts/AuthContext';
import { fetchOrdersByAffiliateWorkerId, deleteOrder } from '@/lib/api-extended';
import OrderDetailsModal from '@/components/orders/OrderDetailsModal';
import { toast } from 'sonner';
import type { Order } from '../../types';

const Buyers = () => {
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [orderDetailsModalOpen, setOrderDetailsModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<Order | null>(null);
  const [deleting, setDeleting] = useState(false);

  const isMobile = useIsMobile();
  const { user, loading: authLoading } = useAuth();

  // Fetch orders when component mounts or user changes
  useEffect(() => {
    if (user?.id) {
      fetchOrders();
    }
  }, [user?.id]);

  const fetchOrders = async () => {
    if (!user?.id) return;

    setLoading(true);
    setError(null);

    try {
      const orders = await fetchOrdersByAffiliateWorkerId(user.id);
      setAllOrders(orders);
    } catch (err: any) {
      console.error('Error fetching orders:', err);
      setError(err.message || 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  // Filter orders based on search term and status
  useEffect(() => {
    let result = allOrders;

    // Filter by search term
    if (searchTerm) {
      result = result.filter(order =>
        order.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer_phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.payment_method.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      result = result.filter(order => order.status === statusFilter);
    }

    setFilteredOrders(result);
  }, [searchTerm, statusFilter, allOrders]);

  const getBadgeVariant = (status: Order['status']) => {
    if (!status) return 'outline';
    switch (status) {
      case 'pending': return 'outline';
      case 'processing': return 'secondary';
      case 'shipped': return 'default';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const getBadgeClassName = (status: Order['status']) => {
    if (!status) return 'border-gray-500 text-gray-700 bg-gray-50';
    switch (status) {
      case 'pending': return 'border-yellow-500 text-yellow-700 bg-yellow-50';
      case 'processing': return 'border-blue-500 text-blue-700 bg-blue-50';
      case 'shipped': return 'border-purple-500 text-purple-700 bg-purple-50';
      case 'delivered': return 'border-green-500 text-green-700 bg-green-50';
      case 'cancelled': return 'border-red-500 text-red-700 bg-red-50';
      default: return 'border-gray-500 text-gray-700 bg-gray-50';
    }
  };

  const getBadgeIcon = (status: Order['status']) => {
    if (!status) return null;
    switch (status) {
      case 'pending': return null;
      case 'processing': return <TrendingUp className="h-3 w-3 ml-1" />;
      case 'shipped': return <Package className="h-3 w-3 ml-1" />;
      case 'delivered': return <DollarSign className="h-3 w-3 ml-1" />;
      case 'cancelled': return null;
      default: return null;
    }
  };

  const handleViewOrderDetails = (orderId: string) => {
    setSelectedOrderId(orderId);
    setOrderDetailsModalOpen(true);
  };

  const handleDeleteOrder = (order: Order) => {
    setOrderToDelete(order);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteOrder = async () => {
    if (!orderToDelete || !user?.id) return;

    setDeleting(true);
    try {
      await deleteOrder(orderToDelete.id, user.id);

      // Remove the order from local state
      setAllOrders(prev => prev.filter(order => order.id !== orderToDelete.id));

      toast.success('Order deleted successfully');
      setDeleteDialogOpen(false);
      setOrderToDelete(null);
    } catch (err: any) {
      console.error('Error deleting order:', err);
      toast.error(err.message || 'Failed to delete order');
    } finally {
      setDeleting(false);
    }
  };

  // Calculate stats from orders
  const stats = {
    totalOrders: allOrders.length,
    totalEarnings: allOrders
      .filter(order => order.status === 'delivered')
      .reduce((sum, order) => sum + (order.affiliate_earnings || 0), 0),
    pendingOrders: allOrders.filter(order => order.status === 'pending').length,
    deliveredOrders: allOrders.filter(order => order.status === 'delivered').length,
  };

  // Show loading state while authentication is loading
  if (authLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Loading...</span>
        </div>
      </Layout>
    );
  }

  // Show message if user is not authenticated
  if (!user) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-8">
          <p className="text-muted-foreground mb-4">Please log in to view your orders.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-2">
          <div>
            <h1 className="text-3xl font-bold mb-1">My Orders</h1>
            <p className="text-muted-foreground">
              View and track your affiliate orders
            </p>
          </div>
          {error && (
            <Button onClick={fetchOrders} variant="outline" className="gap-2">
              <TrendingUp className="h-4 w-4" />
              Retry
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Total Orders</p>
              <p className="text-2xl font-bold">{stats.totalOrders}</p>
            </div>
            <div className="bg-primary/10 p-2 rounded-full">
              <Package className="h-5 w-5 text-primary" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-muted-foreground">All time orders</span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Total Earnings</p>
              <p className="text-2xl font-bold">{formatCurrencyDa(stats.totalEarnings)}</p>
            </div>
            <div className="bg-green-500/10 p-2 rounded-full">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-muted-foreground">From delivered orders</span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Pending Orders</p>
              <p className="text-2xl font-bold">{stats.pendingOrders}</p>
            </div>
            <div className="bg-amber-500/10 p-2 rounded-full">
              <Calendar className="h-5 w-5 text-amber-600" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-muted-foreground">Awaiting processing</span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Delivered Orders</p>
              <p className="text-2xl font-bold">{stats.deliveredOrders}</p>
            </div>
            <div className="bg-blue-500/10 p-2 rounded-full">
              <TrendingUp className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-muted-foreground">Successfully completed</span>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search orders by customer name, phone, order ID..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span>
                {statusFilter === 'all' ? 'All Statuses' :
                 statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
              </span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="processing">Processing</SelectItem>
            <SelectItem value="shipped">Shipped</SelectItem>
            <SelectItem value="delivered">Delivered</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Orders Table */}
      <Card className="overflow-hidden">
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Loading orders...</span>
          </div>
        )}

        {error && (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchOrders} variant="outline">
              Try Again
            </Button>
          </div>
        )}

        {!loading && !error && (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Customer</TableHead>
                  {!isMobile && <TableHead>Date</TableHead>}
                  <TableHead>Items</TableHead>
                  <TableHead>Earn</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={isMobile ? 6 : 7} className="text-center py-8">
                      <p className="text-muted-foreground">
                        {allOrders.length === 0 ? 'No orders found' : 'No orders match your search criteria'}
                      </p>
                      {allOrders.length > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-4"
                          onClick={() => {
                            setSearchTerm('');
                            setStatusFilter('all');
                          }}
                        >
                          Reset Filters
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">#{order.id.slice(0, 8)}</p>
                          <p className="text-sm text-muted-foreground">{order.payment_method}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{order.customer_name}</p>
                          {order.customer_phone && (
                            <p className="text-sm text-muted-foreground">{order.customer_phone}</p>
                          )}
                        </div>
                      </TableCell>
                      {!isMobile && <TableCell>{formatDate(order.order_date)}</TableCell>}
                      <TableCell>
                        <span className="font-medium">{order.items_count}</span>
                        <span className="text-sm text-muted-foreground ml-1">
                          {order.items_count === 1 ? 'item' : 'items'}
                        </span>
                      </TableCell>
                      <TableCell className="font-medium">{formatCurrencyDa(order.affiliate_earnings || 0)}</TableCell>
                      <TableCell>
                        <Badge
                          variant={getBadgeVariant(order.status)}
                          className={`flex items-center w-fit ${getBadgeClassName(order.status)}`}
                        >
                          {order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Unknown'}
                          {getBadgeIcon(order.status)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewOrderDetails(order.id)}
                            className="hover:bg-primary/10"
                            disabled={deleting && orderToDelete?.id === order.id}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          {order.status === 'pending' && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="hover:bg-primary/10"
                                  disabled={deleting && orderToDelete?.id === order.id}
                                >
                                  {deleting && orderToDelete?.id === order.id ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <MoreHorizontal className="h-4 w-4" />
                                  )}
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  className="gap-2 text-red-600 focus:text-red-600"
                                  onClick={() => handleDeleteOrder(order)}
                                  disabled={deleting}
                                >
                                  {deleting && orderToDelete?.id === order.id ? (
                                    <>
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                      Deleting...
                                    </>
                                  ) : (
                                    <>
                                      <Trash2 className="h-4 w-4" />
                                      Delete Order
                                    </>
                                  )}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </Card>

      {/* Order Details Modal */}
      <OrderDetailsModal
        open={orderDetailsModalOpen}
        onOpenChange={setOrderDetailsModalOpen}
        orderId={selectedOrderId}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Order</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete order #{orderToDelete?.id.slice(0, 8)}?
              This action cannot be undone and will permanently remove the order and all its items.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteOrder}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {deleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Order
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default Buyers;
