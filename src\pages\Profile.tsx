import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from 'sonner';
import { User, Loader2, Pencil } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const Profile = () => {
  const { user, profile, profileLoading, updateProfile } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    rip: '',
    confirmRip: '',
  });

  const [ripError, setRipError] = useState<string | null>(null);
  const [isEditingRip, setIsEditingRip] = useState(false);

  // Update form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData(prev => ({
        ...prev,
        name: profile.name || '',
        phone: profile.phone || '',
        rip: profile.rip || '',
        confirmRip: '',
      }));
      setIsEditingRip(false);
    }
  }, [profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Name is required');
      return;
    }

    setIsUpdating(true);

    try {
      const { error } = await updateProfile({
        name: formData.name.trim(),
        phone: formData.phone.trim() || null,
      });

      if (error) {
        toast.error(error.message || 'Failed to update profile');
      } else {
        toast.success('Profile updated successfully!');
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.currentPassword) {
      toast.error('Current password is required');
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (formData.newPassword.length < 6) {
      toast.error('Password must be at least 6 characters');
      return;
    }

    setIsUpdating(true);

    try {
      // Note: Supabase doesn't provide a direct way to change password with current password verification
      // This would typically require a backend endpoint or using Supabase's password reset flow
      toast.info('Password change functionality requires email verification. Please use the forgot password option.');

      // Reset password fields
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      }));
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsUpdating(false);
    }
  };

  // Generate user initials for avatar
  const getUserInitials = () => {
    if (profile?.name) {
      return profile.name
        .split(' ')
        .map(name => name.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    return 'U';
  };

  // Format join date
  const formatJoinDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (profileLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Loading profile...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-1">My Profile</h1>
        <p className="text-muted-foreground">
          Manage your account information and settings
        </p>
      </div>

      <div className="mb-8">
        <Card className="border-t-4 border-t-primary overflow-hidden">
          <CardContent className="p-0">
            <div className="bg-gradient-to-r from-primary/10 to-transparent p-6 sm:p-8">
              <div className="flex flex-col md:flex-row gap-8 items-center md:items-start">
                <div className="flex flex-col items-center">
                  <Avatar className="w-32 h-32 mb-4 border-4 border-background shadow-lg">
                    <AvatarFallback className="text-xl font-bold bg-primary/10 text-primary">
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex flex-col items-center mt-2">
                    <p className="text-sm font-medium text-primary mb-1 capitalize">
                      {profile?.role || 'User'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Member since {formatJoinDate(profile?.created_at)}
                    </p>
                  </div>
                </div>

                <div className="flex-1 text-center md:text-left">
                  <h2 className="text-2xl font-bold mb-1">{profile?.name || 'User'}</h2>
                  <p className="text-muted-foreground mb-4">{user?.email || 'No email'}</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-background/50 p-3 rounded-lg">
                      <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Email</h3>
                      <p className="font-medium">{user?.email || 'Not provided'}</p>
                    </div>
                    <div className="bg-background/50 p-3 rounded-lg">
                      <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Phone</h3>
                      <p className="font-medium">{profile?.phone || 'Not provided'}</p>
                    </div>
                    <div className="bg-background/50 p-3 rounded-lg">
                      <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Join Date</h3>
                      <p className="font-medium">{formatJoinDate(profile?.created_at)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="account" className="w-full">
        <TabsList className="mb-8">
          <TabsTrigger value="account">Account Settings</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="payment">Payment</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>Update your personal information</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={user?.email || ''}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">
                      Email cannot be changed. Contact support if you need to update your email.
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Update Profile'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>Update your account password</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordUpdate} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={formData.currentPassword}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      name="newPassword"
                      type="password"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Change Password'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment">
          <Card>
            <CardHeader>
              <CardTitle>Payment</CardTitle>
              <CardDescription>Set your RIP number for payments</CardDescription>
            </CardHeader>
            <CardContent>
              {!isEditingRip ? (
                <div className="space-y-2">
                  <Label htmlFor="rip">RIP</Label>
                  <div className="flex items-center gap-2">
                    <span className="bg-muted px-2 py-1 rounded text-sm">00799999</span>
                    <Input
                      id="rip"
                      name="rip"
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      minLength={12}
                      maxLength={12}
                      value={formData.rip?.replace(/^00799999/, '') || ''}
                      readOnly
                      className="w-40 bg-muted cursor-not-allowed"
                    />
                    <button
                      type="button"
                      className="ml-2 p-1 rounded hover:bg-muted"
                      aria-label="Edit RIP"
                      onClick={() => setIsEditingRip(true)}
                    >
                      <Pencil className="w-4 h-4 text-primary" />
                    </button>
                  </div>
                </div>
              ) : (
                <form
                  onSubmit={async (e) => {
                    e.preventDefault();
                    setRipError(null);
                    const ripValue = formData.rip?.replace(/^00799999/, '') || '';
                    const confirmRipValue = formData.confirmRip?.replace(/^00799999/, '') || '';
                    if (ripValue.length !== 12 || confirmRipValue.length !== 12) {
                      setRipError('RIP must be exactly 12 digits.');
                      return;
                    }
                    if (ripValue !== confirmRipValue) {
                      setRipError('RIP numbers do not match.');
                      return;
                    }
                    setIsUpdating(true);
                    try {
                      const { error } = await updateProfile({ rip: formData.rip });
                      if (error) {
                        toast.error(error.message || 'Failed to update RIP');
                      } else {
                        toast.success('RIP updated successfully!');
                        setIsEditingRip(false);
                        setFormData(prev => ({ ...prev, confirmRip: '' }));
                      }
                    } catch (error) {
                      toast.error('An unexpected error occurred');
                    } finally {
                      setIsUpdating(false);
                    }
                  }}
                  className="space-y-4"
                >
                  <div className="space-y-2">
                    <Label htmlFor="rip">RIP</Label>
                    <div className="flex items-center gap-2">
                      <span className="bg-muted px-2 py-1 rounded text-sm">00799999</span>
                      <Input
                        id="rip"
                        name="rip"
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        minLength={12}
                        maxLength={12}
                        value={formData.rip?.replace(/^00799999/, '') || ''}
                        onChange={e => {
                          let val = e.target.value.replace(/\D/g, '').slice(0, 12);
                          setFormData(prev => ({ ...prev, rip: '00799999' + val.replace(/^00799999/, '') }));
                        }}
                        placeholder="Enter your number"
                        className="w-40"
                      />
                    </div>
                    <div className="mt-4">
                      <Label htmlFor="confirmRip">Confirm RIP</Label>
                      <div className="flex items-center gap-2">
                        <span className="bg-muted px-2 py-1 rounded text-sm">00799999</span>
                        <Input
                          id="confirmRip"
                          name="confirmRip"
                          type="text"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          minLength={12}
                          maxLength={12}
                          value={formData.confirmRip?.replace(/^00799999/, '') || ''}
                          onChange={e => {
                            let val = e.target.value.replace(/\D/g, '').slice(0, 12);
                            setFormData(prev => ({ ...prev, confirmRip: '00799999' + val.replace(/^00799999/, '') }));
                          }}
                          placeholder="Re-enter your number"
                          className="w-40"
                        />
                      </div>
                    </div>
                    {ripError && <p className="text-xs text-red-500 mt-2">{ripError}</p>}
                  </div>
                  <Button type="submit" disabled={isUpdating}>
                    {isUpdating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      'Save RIP'
                    )}
                  </Button>
                </form>
              )}
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>
    </Layout>
  );
};

export default Profile;
