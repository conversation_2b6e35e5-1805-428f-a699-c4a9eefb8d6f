import { useState, useEffect } from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Dialog, DialogHeader, DialogTitle, DialogPortal, DialogOverlay } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Check, ShoppingCart, X, Minus, Plus, User, Phone, MapPin } from "lucide-react";
import { fetchDeliveryLocations, createOrder } from "@/lib/api-extended";
import { createCustomer } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";
import { cn } from "@/lib/utils";
import type { DeliveryLocation, Commune, CreateOrderForm, Customer } from "../../../types";

interface AddBuyerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId: string;
  productName: string;
  productPrice: number;
  affiliateEarningPrice?: number;
  availableColors?: string[];
  availableSizes?: string[];
  productImage?: string;
}

// Custom DialogContent without default close button
const CustomDialogContent = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-[calc(100%-32px)] max-w-lg max-h-[calc(100vh-40px)] translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg overflow-hidden",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
);

const AddBuyerDialog = ({
  open,
  onOpenChange,
  productId,
  productName,
  productPrice,
  affiliateEarningPrice = productPrice * 0.1,
  availableColors = [],
  availableSizes = [],
  productImage
}: AddBuyerDialogProps) => {
  // Auth context
  const { user } = useAuth();

  // Form state
  const [buyerName, setBuyerName] = useState("");
  const [buyerPhone, setBuyerPhone] = useState("");
  const [selectedWilaya, setSelectedWilaya] = useState<string | null>(null); // Changed to string for wilaya_code
  const [selectedCommune, setSelectedCommune] = useState<string | null>(null); // Changed from selectedCity
  const [address, setAddress] = useState("");
  const [deliveryType, setDeliveryType] = useState<"home" | "desk">("home");
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [notes, setNotes] = useState("");
  const [quantity, setQuantity] = useState(1);

  // Phone validation state
  const [phoneError, setPhoneError] = useState<string>("");
  const [isPhoneValid, setIsPhoneValid] = useState(false);

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Data state
  const [deliveryLocations, setDeliveryLocations] = useState<DeliveryLocation[]>([]);
  const [communes, setCommunes] = useState<Commune[]>([]);
  const [loadingLocations, setLoadingLocations] = useState(true);
  const [loadingCommunes, setLoadingCommunes] = useState(false);

  // Derived state
  const [deliveryPrice, setDeliveryPrice] = useState<number>(0);
  const [itemPrice, setItemPrice] = useState<number>(productPrice);

  // Fallback image
  const fallbackImage = `https://picsum.photos/seed/${productId}/200/200`;

  // Mobile detection is handled by CSS responsive design

  // Phone validation functions
  const validateAlgerianPhone = (phone: string): { isValid: boolean; error: string } => {
    // Remove any spaces or special characters for validation
    const cleanPhone = phone.replace(/\s+/g, '');

    // Check if phone is empty
    if (!cleanPhone) {
      return { isValid: false, error: "" };
    }

    // Check if phone contains only digits
    if (!/^\d+$/.test(cleanPhone)) {
      return { isValid: false, error: "Phone number must contain only digits" };
    }

    // Check length (must be exactly 10 digits)
    if (cleanPhone.length !== 10) {
      return { isValid: false, error: "Phone number must be exactly 10 digits long" };
    }

    // Check if it starts with valid Algerian prefixes (05, 06, 07)
    if (!cleanPhone.startsWith('05') && !cleanPhone.startsWith('06') && !cleanPhone.startsWith('07')) {
      return { isValid: false, error: "Phone number must start with 05, 06, or 07" };
    }

    return { isValid: true, error: "" };
  };

  const handlePhoneChange = (value: string) => {
    // Allow only digits and limit to 10 characters
    const digitsOnly = value.replace(/\D/g, '').slice(0, 10);
    setBuyerPhone(digitsOnly);

    // Validate the phone number
    const validation = validateAlgerianPhone(digitsOnly);
    setIsPhoneValid(validation.isValid);
    setPhoneError(validation.error);
  };

  // Load delivery locations on component mount
  useEffect(() => {
    const loadDeliveryLocations = async () => {
      try {
        setLoadingLocations(true);
        const locations = await fetchDeliveryLocations();
        // Filter only enabled locations
        const enabledLocations = locations.filter(location => location.enabled);
        setDeliveryLocations(enabledLocations);
      } catch (error) {
        console.error('Error loading delivery locations:', error);
        toast.error('Failed to load delivery locations');
      } finally {
        setLoadingLocations(false);
      }
    };

    loadDeliveryLocations();
  }, []);

  // Load commune data from JSON file
  const loadCommuneData = async (wilayaCode: string) => {
    try {
      setLoadingCommunes(true);
      // Import the commune data
      const communeData = await import('@/data/communeData.json');
      // Filter communes by wilaya_id
      const wilayaCommunes = communeData.default.filter(
        (commune: Commune) => commune.wilaya_id === wilayaCode
      );
      setCommunes(wilayaCommunes);
    } catch (error) {
      console.error('Error loading commune data:', error);
      toast.error('Failed to load commune data');
      setCommunes([]);
    } finally {
      setLoadingCommunes(false);
    }
  };

  // Check if all required fields are filled
  const isFormValid = () => {
    const basicFieldsValid = buyerName.trim() !== "" &&
                            buyerPhone.trim() !== "" &&
                            isPhoneValid &&
                            selectedWilaya !== null &&
                            selectedCommune !== null;

    // For home delivery, address is also required
    if (deliveryType === "home") {
      return basicFieldsValid && address.trim() !== "";
    }

    // For desk delivery, address is not required
    return basicFieldsValid;
  };



  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      setBuyerName("");
      setBuyerPhone("");
      setPhoneError("");
      setIsPhoneValid(false);
      setSelectedWilaya(null);
      setSelectedCommune(null);
      setAddress("");
      setDeliveryType("home");
      setSelectedColor(null);
      setSelectedSize(null);
      setQuantity(1);
      setIsSubmitting(false);
      setCommunes([]);
      setDeliveryPrice(0);
    }
  }, [open]);

  // Clear address when delivery type changes to desk
  useEffect(() => {
    if (deliveryType === "desk") {
      setAddress("");
    }
  }, [deliveryType]);

  // Update communes when wilaya changes
  useEffect(() => {
    if (selectedWilaya) {
      // Load commune data for the selected wilaya
      loadCommuneData(selectedWilaya);
      setSelectedCommune(null);
    } else {
      setCommunes([]);
      setSelectedCommune(null);
    }
  }, [selectedWilaya]);

  // Update delivery price when wilaya or delivery type changes
  useEffect(() => {
    if (selectedWilaya && deliveryLocations.length > 0) {
      const selectedLocation = deliveryLocations.find(
        location => location.wilaya_code.toString() === selectedWilaya
      );

      if (selectedLocation) {
        const price = deliveryType === "home"
          ? selectedLocation.domicile_price
          : selectedLocation.desk_price;
        setDeliveryPrice(price);
      }
    } else {
      setDeliveryPrice(0);
    }
  }, [selectedWilaya, deliveryType, deliveryLocations]);

  // Update item price when quantity changes
  useEffect(() => {
    setItemPrice(productPrice * quantity);
  }, [productPrice, quantity]);

  // Total price is calculated inline in the UI



  // Quantity handlers
  const incrementQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!buyerName || !buyerPhone || !selectedWilaya || !selectedCommune) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Validate phone number format
    if (!isPhoneValid) {
      toast.error("Please enter a valid Algerian phone number (10 digits starting with 05, 06, or 07)");
      return;
    }

    // Address is only required for home delivery
    if (deliveryType === "home" && !address) {
      toast.error("Please enter a detailed address for home delivery");
      return;
    }

    // Check if user is authenticated
    if (!user) {
      toast.error("You must be logged in to place an order");
      return;
    }

    setIsSubmitting(true);

    try {
      // Find the selected wilaya and commune data
      const selectedLocation = deliveryLocations.find(
        location => location.wilaya_code.toString() === selectedWilaya
      );
      const selectedCommuneData = communes.find(
        commune => commune.id === selectedCommune
      );

      // Create or find existing customer
      const customerLocation = `${selectedLocation?.wilaya_name || ""}, ${selectedCommuneData?.name || ""}`;

      let customer: Customer;
      try {
        // First, try to find existing customer by phone
        const { fetchCustomers } = await import('@/lib/api');
        const existingCustomers = await fetchCustomers();
        const existingCustomer = existingCustomers.find(c => c.phone === buyerPhone);

        if (existingCustomer) {
          customer = existingCustomer;
          console.log('Using existing customer:', customer.id);
        } else {
          // Create a new customer (no email needed)
          customer = await createCustomer({
            name: buyerName,
            phone: buyerPhone,
            location: customerLocation,
            address: deliveryType === "home" ? address : undefined
          });
          console.log('Created new customer:', customer.id);
        }
      } catch (error: any) {
        console.error('Error creating/finding customer:', error);
        throw new Error('Failed to create or find customer. Please try again.');
      }

      // Prepare order data
      const orderData: CreateOrderForm = {
        customer_id: customer.id,
        affiliate_worker_id: user.id,
        payment_method: "Cash on Delivery", // Default payment method
        shipping_cost: deliveryPrice,
        notes: notes || `Delivery Type: ${deliveryType}${selectedColor ? `, Color: ${selectedColor}` : ""}${selectedSize ? `, Size: ${selectedSize}` : ""}`,
        items: [{
          product_id: productId,
          quantity: quantity,
          price: productPrice,
          affiliate_earnings: affiliateEarningPrice * quantity
        }]
      };

      // Create the order
      const order = await createOrder(orderData);

      toast.success(`Order for ${buyerName} added successfully! Order ID: ${order.id.slice(0, 8)}`);

      // Close dialog (form will be reset by useEffect)
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error creating order:", error);
      toast.error(error.message || "Failed to create order. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent className="sm:max-w-[600px] w-[calc(100%-32px)] max-w-[95vw] max-h-[90vh] flex flex-col bg-white p-0 overflow-hidden">
        <DialogHeader className="border-b border-gray-300 pb-4 bg-white flex-shrink-0 px-6 pt-6 relative">
          <DialogTitle className="text-xl flex items-center gap-2 text-black pr-10">
            <User className="h-6 w-6 text-black" />
            Add Buyer
          </DialogTitle>
          {/* Custom close button to ensure proper positioning */}
          <button
            onClick={() => onOpenChange(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none p-1 hover:bg-gray-100"
          >
            <X className="h-4 w-4 text-black" />
            <span className="sr-only">Close</span>
          </button>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto scrollbar-hide">
          <div className="space-y-6 py-4 px-6">

            {/* Product Summary */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                <ShoppingCart className="h-5 w-5 text-black" />
                Product Information
              </h3>
              <div className="border border-gray-300 rounded-lg p-4 bg-white">
                <div className="flex flex-wrap sm:flex-nowrap items-center gap-3">
                  <div className="relative h-16 w-16 flex-shrink-0 rounded-md overflow-hidden border border-gray-300">
                    <img
                      src={productImage || fallbackImage}
                      alt={productName}
                      className="h-full w-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = fallbackImage;
                      }}
                    />
                  </div>
                  <div className="flex-1 min-w-0 w-[calc(100%-80px)] sm:w-auto">
                    <h4 className="font-medium text-black">{productName}</h4>
                    <p className="text-sm text-gray-600">{productPrice.toFixed(2)} Da</p>
                  </div>
                  <div className="flex items-center border border-gray-300 rounded-md mt-2 sm:mt-0 ml-auto">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-none hover:bg-gray-100"
                      onClick={decrementQuantity}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="w-8 text-center text-sm text-black">{quantity}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-none hover:bg-gray-100"
                      onClick={incrementQuantity}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Buyer Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <User className="h-5 w-5 text-black" />
                  Buyer Information
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="buyerName" className="text-black">
                        Name <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="buyerName"
                          value={buyerName}
                          onChange={(e) => setBuyerName(e.target.value)}
                          placeholder="Enter buyer's name"
                          className="pl-10 border-gray-300 focus:border-black"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="buyerPhone" className="text-black">
                        Phone <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="buyerPhone"
                          value={buyerPhone}
                          onChange={(e) => handlePhoneChange(e.target.value)}
                          placeholder="0551234567"
                          className={`pl-10 ${
                            phoneError && buyerPhone
                              ? "border-red-500 focus:border-red-500"
                              : isPhoneValid && buyerPhone
                              ? "border-green-500 focus:border-green-500"
                              : "border-gray-300 focus:border-black"
                          }`}
                          maxLength={10}
                          inputMode="numeric"
                          pattern="[0-9]*"
                          required
                        />
                        {isPhoneValid && buyerPhone && (
                          <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                        )}
                      </div>
                      {phoneError && buyerPhone && (
                        <p className="text-sm text-red-500 mt-1">Enter a valid number</p>
                      )}
                      {!phoneError && !isPhoneValid && buyerPhone && (
                        <p className="text-sm text-gray-500 mt-1">
                          Format: 10 digits starting with 05, 06, or 07
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Location Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <MapPin className="h-5 w-5 text-black" />
                  Location Information
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="wilaya" className="text-black">
                        Wilaya <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={selectedWilaya || ""}
                        onValueChange={(value) => setSelectedWilaya(value)}
                        disabled={loadingLocations}
                      >
                        <SelectTrigger id="wilaya" className="border-gray-300 focus:border-black">
                          <SelectValue placeholder={loadingLocations ? "Loading..." : "Select wilaya"} />
                        </SelectTrigger>
                        <SelectContent className="max-h-[40vh] bg-white border border-gray-300">
                          {deliveryLocations.map((location) => (
                            <SelectItem key={location.wilaya_code} value={location.wilaya_code.toString()}>
                              {location.wilaya_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="commune" className="text-black">
                        Commune <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={selectedCommune || ""}
                        onValueChange={(value) => setSelectedCommune(value)}
                        disabled={!selectedWilaya || loadingCommunes}
                      >
                        <SelectTrigger id="commune" className="border-gray-300 focus:border-black">
                          <SelectValue placeholder={
                            !selectedWilaya
                              ? "Select wilaya first"
                              : loadingCommunes
                                ? "Loading communes..."
                                : "Select commune"
                          } />
                        </SelectTrigger>
                        <SelectContent className="max-h-[40vh] bg-white border border-gray-300">
                          {communes.map((commune) => (
                            <SelectItem key={commune.id} value={commune.id}>
                              {commune.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Delivery Type Selection */}
                  <div className="space-y-3 pt-4 border-t border-gray-200">
                    <Label className="text-black font-medium">
                      Delivery Type <span className="text-red-500">*</span>
                    </Label>
                    <RadioGroup
                      value={deliveryType}
                      onValueChange={(value) => setDeliveryType(value as "home" | "desk")}
                      className="flex flex-col space-y-3"
                    >
                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <RadioGroupItem value="home" id="home" className="border-gray-400" />
                        <Label htmlFor="home" className="cursor-pointer text-black flex-1">
                          <div className="font-medium">Home Delivery</div>
                          <div className="text-sm text-gray-600">Delivered to your doorstep</div>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <RadioGroupItem value="desk" id="desk" className="border-gray-400" />
                        <Label htmlFor="desk" className="cursor-pointer text-black flex-1">
                          <div className="font-medium">Desk Delivery</div>
                          <div className="text-sm text-gray-600">Pick up from delivery center</div>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Address Fields - Only show for home delivery */}
                  {deliveryType === "home" && (
                    <div className="space-y-4 pt-4 border-t border-gray-200">
                      <div className="space-y-2">
                        <Label htmlFor="address" className="text-black">
                          Detailed Address <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Textarea
                            id="address"
                            value={address}
                            onChange={(e) => setAddress(e.target.value)}
                            placeholder="Enter detailed address"
                            className="pl-10 border-gray-300 focus:border-black resize-none"
                            rows={3}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Product Options */}
              {(availableColors.length > 0 || availableSizes.length > 0) && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                    <Check className="h-5 w-5 text-black" />
                    Product Options
                  </h3>
                  <div className="border border-gray-300 rounded-lg p-4 bg-white">
                    <div className="space-y-4">
                      {availableColors.length > 0 && (
                        <div className="space-y-2">
                          <Label htmlFor="color" className="text-black">Color (Optional)</Label>
                          <Select
                            value={selectedColor || ""}
                            onValueChange={setSelectedColor}
                          >
                            <SelectTrigger id="color" className="border-gray-300 focus:border-black">
                              <SelectValue placeholder="Select color" />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-300">
                              {availableColors.map((color) => (
                                <SelectItem key={color} value={color}>
                                  {color}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {availableSizes.length > 0 && (
                        <div className="space-y-2">
                          <Label htmlFor="size" className="text-black">Size (Optional)</Label>
                          <Select
                            value={selectedSize || ""}
                            onValueChange={setSelectedSize}
                          >
                            <SelectTrigger id="size" className="border-gray-300 focus:border-black">
                              <SelectValue placeholder="Select size" />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-300">
                              {availableSizes.map((size) => (
                                <SelectItem key={size} value={size}>
                                  {size}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Additional Notes */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <Check className="h-5 w-5 text-black" />
                  Additional Notes
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="space-y-2">
                    <Label htmlFor="notes" className="text-black">Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Any additional notes or special instructions..."
                      className="border-gray-300 focus:border-black resize-none"
                      rows={3}
                    />
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-black">
                  <ShoppingCart className="h-5 w-5 text-black" />
                  Order Summary
                </h3>
                <div className="border border-gray-300 rounded-lg p-4 bg-white">
                  <div className="space-y-3">
                    <div className="flex justify-between text-black">
                      <span>Item Price:</span>
                      <span>{productPrice.toFixed(2)} Da</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Quantity:</span>
                      <span>x{quantity}</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Subtotal:</span>
                      <span>{itemPrice.toFixed(2)} Da</span>
                    </div>
                    <div className="flex justify-between text-black">
                      <span>Delivery Fee:</span>
                      <span>{deliveryPrice.toFixed(2)} Da</span>
                    </div>
                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between font-bold text-lg text-black">
                        <span>Total:</span>
                        <span>{(itemPrice + deliveryPrice).toFixed(2)} Da</span>
                      </div>
                    </div>
                    <div className="border-t border-gray-200 pt-3 mt-3">
                      <div className="flex justify-between text-green-600 font-semibold">
                        <span>Your Earnings:</span>
                        <span>{(affiliateEarningPrice * quantity).toFixed(2)} Da</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Fixed Bottom Action Buttons - Always Visible */}
        <div className="border-t border-gray-300 bg-white p-4 flex items-center justify-end gap-2 flex-shrink-0">
          <Button
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            variant="outline"
            size="sm"
            className={`border-gray-300 text-black ${
              isSubmitting
                ? 'opacity-50 cursor-not-allowed hover:bg-white'
                : 'hover:bg-gray-100'
            }`}
          >
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid() || isSubmitting}
            size="sm"
            className={`border-black ${
              !isFormValid() || isSubmitting
                ? 'bg-gray-400 hover:bg-gray-400 text-gray-200 cursor-not-allowed border-gray-400'
                : 'bg-black hover:bg-gray-800 text-white'
            }`}
          >
            {isSubmitting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <ShoppingCart className="h-4 w-4 mr-1" />
            )}
            Place Order
          </Button>
        </div>
      </CustomDialogContent>
    </Dialog>
  );
};

export default AddBuyerDialog;
