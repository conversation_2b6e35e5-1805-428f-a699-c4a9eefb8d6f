import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { Heart, Star, Loader2, ArrowLeft } from "lucide-react";
import { getEnhancedProductById } from '@/lib/api';
import { formatCurrencyDa } from '@/lib/utils';
import AffiliateOrderForm from '@/components/product/AffiliateOrderForm';

const AffiliateLanding = () => {
  const { userId, productId } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true);

      try {
        // Fetch product details from API
        const productData = await getEnhancedProductById(productId as string);
        setProduct(productData);
      } catch (error) {
        console.error('Error fetching product:', error);
        toast.error("Product not found");
        navigate('/');
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading product...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg font-medium mb-2">Product not found</p>
          <p className="text-muted-foreground mb-4">The product you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/')}>Go to Homepage</Button>
        </div>
      </div>
    );
  }

  const commissionAmount = (product.price * product.commission) / 100;
  const productName = product.title || product.name;
  const productImages = product.images || [product.image];
  const fallbackImage = `https://picsum.photos/seed/${product.id}/800/600`;

  const handleOrderSuccess = (newOrderId: string) => {
    setOrderId(newOrderId);
    setOrderSuccess(true);
    setShowOrderForm(false);
    toast.success("Order placed successfully!");
  };

  const handleOrderCancel = () => {
    setShowOrderForm(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
              >
                <ArrowLeft size={16} className="mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-lg font-semibold">Product Details</h1>
                <p className="text-sm text-muted-foreground">Shared by affiliate</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-square rounded-lg overflow-hidden bg-white border">
              <img
                src={productImages[selectedImage] || product.image}
                alt={productName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = fallbackImage;
                }}
              />
            </div>

            {productImages.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {productImages.map((image: string, index: number) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`aspect-square rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-black' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${productName} ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = fallbackImage;
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">{productName}</h1>
              
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                  <span className="ml-1 text-sm font-medium">{product.rating.rate}</span>
                </div>
                <span className="text-sm text-muted-foreground">({product.rating.count} reviews)</span>

                <Badge
                  variant="secondary"
                  className="text-xs px-2.5 py-1 ml-2"
                >
                  {product.category}
                </Badge>
              </div>

              <div className="mb-6">
                <p className="text-3xl font-bold text-black">{formatCurrencyDa(product.price)}</p>
              </div>

              <div className="prose prose-sm max-w-none">
                <p className="text-gray-600">{product.description}</p>
              </div>
            </div>

            {/* Order Button */}
            <div className="space-y-4">
              <Button
                size="lg"
                className="w-full"
                onClick={() => setShowOrderForm(true)}
              >
                Order Now
              </Button>
              
              <p className="text-xs text-center text-muted-foreground">
                This product is shared by an affiliate partner. 
                Your purchase helps support their work.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Order Form */}
      {showOrderForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <AffiliateOrderForm
            productId={product.id}
            productName={productName}
            productPrice={product.price}
            productImage={product.image}
            affiliateUserId={userId as string}
            availableColors={product.colors || []}
            availableSizes={product.sizes || []}
            onOrderSuccess={handleOrderSuccess}
            onCancel={handleOrderCancel}
          />
        </div>
      )}

      {/* Success Modal */}
      {orderSuccess && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full text-center">
            <div className="mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Order Placed Successfully!</h3>
              <p className="text-sm text-gray-600 mb-4">
                Your order has been received and will be processed soon.
              </p>
              {orderId && (
                <p className="text-xs text-gray-500 mb-4">
                  Order ID: {orderId.slice(0, 8)}...
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Button
                onClick={() => {
                  setOrderSuccess(false);
                  setOrderId(null);
                }}
                className="w-full"
              >
                Continue Shopping
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="w-full"
              >
                Go to Homepage
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AffiliateLanding;
