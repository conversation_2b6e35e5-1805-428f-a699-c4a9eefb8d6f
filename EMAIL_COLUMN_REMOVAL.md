# Email Column Removal from Customers Table

## Overview
Removed the email column from the customers table as requested, since it's not being used and phone is the primary contact method.

## Database Changes

### 1. Database Migration Script
- **File**: `database-remove-customer-email.sql`
- **Actions**:
  - Drop unique constraint on email column
  - Drop the email column from customers table
  - Drop any indexes referencing email
  - Add comment documenting the change

### 2. Updated Database Schema
- **File**: `database-schema.sql`
- **Changes**: Removed email field from customers table definition

## Code Changes

### 1. Type Definitions (`types.ts`)
- **Customer interface**: Removed `email: string` field
- **CreateCustomerForm interface**: Removed `email: string` field
- **Order interface**: Changed `customer_email?: string` to `customer_phone?: string`

### 2. API Functions (`src/lib/api.ts`)
- **createCustomer()**: Removed email from insert statement
- **updateCustomer()**: Email field automatically excluded since it's not in the type

### 3. Extended API Functions (`src/lib/api-extended.ts`)
- **fetchOrders()**: Changed join to select `phone` instead of `email` from customers
- **fetchOrderById()**: Changed join to select `phone` instead of `email` from customers
- **Return objects**: Changed `customer_email` to `customer_phone` in mapped results

### 4. Supabase Types (`src/lib/supabase.ts`)
- **customers.Row**: Removed `email: string` field
- **customers.Insert**: Removed `email: string` field
- **customers.Update**: Removed `email?: string` field

### 5. AddBuyerDialog Component (`src/components/product/AddBuyerDialog.tsx`)
- **Customer creation**: Removed email generation and usage
- **Duplicate handling**: Now finds existing customers by phone instead of email
- **Simplified logic**: No more email-related error handling

### 6. BuyersList Component (`src/components/dashboard/BuyersList.tsx`)
- **Buyer interface**: Changed `email: string` to `phone: string`
- **Display**: Shows phone number instead of email in buyer details

## Migration Steps Required

1. **Run Database Migration**:
   ```sql
   -- Execute the contents of database-remove-customer-email.sql in Supabase SQL editor
   ```

2. **Verify Changes**:
   - Check that customers table no longer has email column
   - Verify that existing customer records are preserved
   - Test order creation functionality

## Benefits

1. **Simplified Data Model**: Removed unused field reduces complexity
2. **No Duplicate Issues**: Eliminates email uniqueness constraint problems
3. **Phone-First Approach**: Aligns with Algerian business practices where phone is primary contact
4. **Cleaner Code**: Removed email-related error handling and generation logic

## Backward Compatibility

- **Database**: Existing customer records will have email column removed but other data preserved
- **API**: All functions now work with phone-based customer identification
- **UI**: Components now display phone instead of email

## Testing Checklist

- [ ] Run database migration script
- [ ] Test customer creation through AddBuyerDialog
- [ ] Verify order creation works without email
- [ ] Check that existing customers can be found by phone
- [ ] Ensure BuyersList displays phone numbers correctly
- [ ] Verify no compilation errors in TypeScript

## Files Modified

1. `database-remove-customer-email.sql` (new)
2. `database-schema.sql`
3. `types.ts`
4. `src/lib/api.ts`
5. `src/lib/api-extended.ts`
6. `src/lib/supabase.ts`
7. `src/components/product/AddBuyerDialog.tsx`
8. `src/components/dashboard/BuyersList.tsx`

The email column has been completely removed from the system and all related code has been updated to use phone numbers as the primary customer contact method.
