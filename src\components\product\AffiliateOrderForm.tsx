import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Check, ShoppingCart, Minus, Plus, User, Phone, MapPin } from "lucide-react";
import { createOrder } from '@/lib/api-extended';
import { createCustomer, fetchDeliveryLocations, fetchCommunesByWilayaId } from '@/lib/api';
import { formatCurrencyDa } from '@/lib/utils';
import type { DeliveryLocation, Commune } from '../../types';

interface AffiliateOrderFormProps {
  productId: string;
  productName: string;
  productPrice: number;
  productImage?: string;
  affiliateUserId: string;
  availableColors?: string[];
  availableSizes?: string[];
  onOrderSuccess: (orderId: string) => void;
  onCancel: () => void;
}

const AffiliateOrderForm = ({
  productId,
  productName,
  productPrice,
  productImage,
  affiliateUserId,
  availableColors = [],
  availableSizes = [],
  onOrderSuccess,
  onCancel
}: AffiliateOrderFormProps) => {
  // Form state
  const [buyerName, setBuyerName] = useState("");
  const [buyerPhone, setBuyerPhone] = useState("");
  const [selectedWilaya, setSelectedWilaya] = useState<string | null>(null);
  const [selectedCommune, setSelectedCommune] = useState<string | null>(null);
  const [address, setAddress] = useState("");
  const [deliveryType, setDeliveryType] = useState<"home" | "desk">("home");
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [notes, setNotes] = useState("");
  const [quantity, setQuantity] = useState(1);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingLocations, setLoadingLocations] = useState(true);
  const [loadingCommunes, setLoadingCommunes] = useState(false);

  // Data states
  const [deliveryLocations, setDeliveryLocations] = useState<DeliveryLocation[]>([]);
  const [communes, setCommunes] = useState<Commune[]>([]);
  const [deliveryPrice, setDeliveryPrice] = useState(0);

  // Calculated values
  const itemPrice = productPrice * quantity;
  const fallbackImage = `https://picsum.photos/seed/${productId}/200/200`;

  // Load delivery locations on mount
  useEffect(() => {
    const loadDeliveryLocations = async () => {
      try {
        setLoadingLocations(true);
        const locations = await fetchDeliveryLocations();
        setDeliveryLocations(locations);
      } catch (error) {
        console.error('Error loading delivery locations:', error);
        toast.error('Failed to load delivery locations');
      } finally {
        setLoadingLocations(false);
      }
    };

    loadDeliveryLocations();
  }, []);

  // Load communes when wilaya changes
  useEffect(() => {
    const loadCommunes = async () => {
      if (!selectedWilaya) {
        setCommunes([]);
        setDeliveryPrice(0);
        return;
      }

      try {
        setLoadingCommunes(true);
        const communesData = await fetchCommunesByWilayaId(selectedWilaya);
        setCommunes(communesData);

        // Update delivery price based on selected wilaya and delivery type
        const location = deliveryLocations.find(loc => loc.wilaya_code.toString() === selectedWilaya);
        if (location) {
          setDeliveryPrice(deliveryType === "home" ? location.domicile_price : location.desk_price);
        }
      } catch (error) {
        console.error('Error loading communes:', error);
        toast.error('Failed to load communes');
      } finally {
        setLoadingCommunes(false);
      }
    };

    loadCommunes();
  }, [selectedWilaya, deliveryType, deliveryLocations]);

  // Reset commune when wilaya changes
  useEffect(() => {
    setSelectedCommune(null);
  }, [selectedWilaya]);

  const incrementQuantity = () => setQuantity(prev => prev + 1);
  const decrementQuantity = () => setQuantity(prev => Math.max(1, prev - 1));

  // Check if form is valid
  const isFormValid = () => {
    const basicFieldsValid = buyerName.trim() !== "" &&
                            buyerPhone.trim() !== "" &&
                            selectedWilaya !== null &&
                            selectedCommune !== null;

    // For home delivery, address is also required
    if (deliveryType === "home") {
      return basicFieldsValid && address.trim() !== "";
    }

    return basicFieldsValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isFormValid()) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);

    try {
      // Get location details
      const location = deliveryLocations.find(loc => loc.wilaya_code.toString() === selectedWilaya);
      const commune = communes.find(c => c.id === selectedCommune);

      if (!location || !commune) {
        throw new Error("Invalid location selection");
      }

      // Create customer first
      const customerData = {
        name: buyerName,
        phone: buyerPhone,
        location: `${commune.name}, ${location.wilaya_name}`,
        address: deliveryType === "home" ? address : undefined
      };

      const customer = await createCustomer(customerData);

      // Calculate affiliate earning price (assuming 10% commission for now)
      const affiliateEarningPrice = productPrice * 0.1;

      // Create order
      const orderData = {
        customer_id: customer.id,
        affiliate_worker_id: affiliateUserId,
        payment_method: "Cash on Delivery",
        shipping_cost: deliveryPrice,
        notes: notes || undefined,
        items: [{
          product_id: productId,
          quantity: quantity,
          price: productPrice,
          affiliate_earnings: affiliateEarningPrice * quantity
        }]
      };

      const order = await createOrder(orderData);
      onOrderSuccess(order.id);

    } catch (error: any) {
      console.error("Error creating order:", error);
      toast.error(error.message || "Failed to create order. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-4 border-b">
        <h2 className="text-xl font-semibold text-gray-900">Complete Your Order</h2>
        <p className="text-sm text-gray-600 mt-1">Fill in your details to place the order</p>
      </div>

      <div className="p-6 space-y-6 max-h-[70vh] overflow-y-auto">
        {/* Product Summary */}
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="h-16 w-16 rounded-md overflow-hidden border">
              <img
                src={productImage || fallbackImage}
                alt={productName}
                className="h-full w-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = fallbackImage;
                }}
              />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{productName}</h3>
              <p className="text-sm text-gray-600">{formatCurrencyDa(productPrice)}</p>
            </div>
            <div className="flex items-center border rounded-md">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={decrementQuantity}
                disabled={quantity <= 1}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-8 text-center text-sm">{quantity}</span>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={incrementQuantity}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Buyer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <User className="h-5 w-5" />
              Your Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="buyerName">
                  Full Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="buyerName"
                  value={buyerName}
                  onChange={(e) => setBuyerName(e.target.value)}
                  placeholder="Enter your full name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="buyerPhone">
                  Phone Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="buyerPhone"
                  value={buyerPhone}
                  onChange={(e) => setBuyerPhone(e.target.value)}
                  placeholder="Enter your phone number"
                  required
                />
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Delivery Location
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="wilaya">
                  Wilaya <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={selectedWilaya || ""}
                  onValueChange={setSelectedWilaya}
                  disabled={loadingLocations}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingLocations ? "Loading..." : "Select wilaya"} />
                  </SelectTrigger>
                  <SelectContent>
                    {deliveryLocations.map((location) => (
                      <SelectItem key={location.wilaya_code} value={location.wilaya_code.toString()}>
                        {location.wilaya_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="commune">
                  Commune <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={selectedCommune || ""}
                  onValueChange={setSelectedCommune}
                  disabled={!selectedWilaya || loadingCommunes}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      !selectedWilaya
                        ? "Select wilaya first"
                        : loadingCommunes
                          ? "Loading..."
                          : "Select commune"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {communes.map((commune) => (
                      <SelectItem key={commune.id} value={commune.id}>
                        {commune.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Delivery Type */}
            <div className="space-y-3">
              <Label>Delivery Type <span className="text-red-500">*</span></Label>
              <RadioGroup
                value={deliveryType}
                onValueChange={(value) => setDeliveryType(value as "home" | "desk")}
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <RadioGroupItem value="home" id="home" />
                  <Label htmlFor="home" className="cursor-pointer flex-1">
                    <div className="font-medium">Home Delivery</div>
                    <div className="text-sm text-gray-600">Delivered to your doorstep</div>
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <RadioGroupItem value="desk" id="desk" />
                  <Label htmlFor="desk" className="cursor-pointer flex-1">
                    <div className="font-medium">Desk Delivery</div>
                    <div className="text-sm text-gray-600">Pick up from delivery center</div>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Address for home delivery */}
            {deliveryType === "home" && (
              <div className="space-y-2">
                <Label htmlFor="address">
                  Detailed Address <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Enter your detailed address"
                  rows={3}
                />
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold mb-3">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Item Price:</span>
                <span>{formatCurrencyDa(productPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span>Quantity:</span>
                <span>x{quantity}</span>
              </div>
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{formatCurrencyDa(itemPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Fee:</span>
                <span>{formatCurrencyDa(deliveryPrice)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>{formatCurrencyDa(itemPrice + deliveryPrice)}</span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-4 border-t flex gap-3">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
          className="flex-1"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid() || isSubmitting}
          className="flex-1"
        >
          {isSubmitting ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <ShoppingCart className="h-4 w-4 mr-2" />
          )}
          Place Order
        </Button>
      </div>
    </div>
  );
};

export default AffiliateOrderForm;
