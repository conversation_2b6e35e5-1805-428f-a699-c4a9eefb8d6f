// Type definitions for the application

export interface DeliveryLocation {
  id: string;
  wilaya_code: number;
  wilaya_name: string;
  desk_price: number;
  domicile_price: number;
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  affiliate_earning_price: number;
  category_id: string;
  main_image: string;
  gallery_images?: string[];
  in_stock: boolean;
  stock_count: number;
  free_shipping: boolean;
  shipping_cost: number;
  rating_rate: number;
  rating_count: number;
  created_at: string;
  updated_at: string;
  // Additional computed fields
  category?: string;
  rating?: {
    rate: number;
    count: number;
  };
  image?: string; // For backward compatibility
  commission?: number; // For backward compatibility
  stockCount?: number; // For backward compatibility
  inStock?: boolean; // For backward compatibility
}

export interface Category {
  id: string;
  name: string;
  image?: string | null;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  location: string;
  address?: string;
  join_date: string;
  total_orders: number;
  total_spent: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface AffiliateWorker {
  id: string;
  name: string;
  phone: string;
  role: string;
}

export interface Order {
  id: string;
  customer_id: string;
  affiliate_worker_id?: string;
  order_date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | null;
  total: number;
  items_count: number;
  payment_method: string;
  shipping_cost: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  // Additional fields from joins
  customer_name?: string;
  customer_phone?: string;
  customer_location?: string;
  customer_address?: string;
  affiliate_worker?: AffiliateWorker;
  order_items?: OrderItem[];
  affiliate_earnings?: number; // Calculated total earnings from all order items
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  affiliate_earnings: number;
  created_at: string;
  // Additional fields from joins
  product_name?: string;
  product_image?: string;
}

export interface Payment {
  id: string;
  affiliate_worker_id: string;
  amount: number;
  payment_date: string;
  payment_method?: string;
  status: 'pending' | 'paid' | 'cancelled';
  notes?: string;
  created_at: string;
  updated_at: string;
  // Additional fields from joins
  affiliate_worker?: AffiliateWorker;
}

export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  conversionRate: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
  conversionGrowth: number;
}

// Form types
export interface CreateProductForm {
  title: string;
  description: string;
  price: number;
  affiliate_earning_price: number;
  category_id: string;
  main_image: File | string;
  gallery_images?: (File | string)[];
  in_stock?: boolean;
  stock_count?: number;
  free_shipping?: boolean;
  shipping_cost?: number;
}

export interface CreateCategoryForm {
  name: string;
  image?: File | string | null;
}

export interface CreateCustomerForm {
  name: string;
  phone: string;
  location: string;
  address?: string;
}

export interface CreateOrderForm {
  customer_id: string;
  affiliate_worker_id?: string;
  payment_method: string;
  shipping_cost: number;
  notes?: string;
  items: {
    product_id: string;
    quantity: number;
    price: number;
    affiliate_earnings?: number;
  }[];
}

// Search and pagination types
export interface SearchFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  sortBy?: 'price' | 'rating' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Commune data interface
export interface Commune {
  id: string;
  post_code: string;
  name: string;
  wilaya_id: string;
  ar_name: string;
  longitude: string;
  latitude: string;
}
