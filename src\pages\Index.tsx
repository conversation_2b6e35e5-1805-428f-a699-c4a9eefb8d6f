import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { useAuth } from '@/contexts/AuthContext';
import StatsCard from '@/components/dashboard/StatsCard';
import ProductCard from '@/components/dashboard/ProductCard';
import BuyersList from '@/components/dashboard/BuyersList';
import EarningsChart from '@/components/dashboard/EarningsChart';
import {
  Users,
  DollarSign,
  MousePointer,
  Package2,
  Plus
} from "lucide-react";
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  formatCurrency,
  formatCurrencyDa,
  generateRandomData,
  generateMockBuyers
} from '@/lib/utils';
import { getEnhancedProducts } from '@/lib/api';
import { useIsMobile } from '@/hooks/use-mobile';
import { fetchDashboardStats, fetchOrdersByAffiliateWorkerId, getUserTotalEarnings, getUserPendingPayments } from '@/lib/api-extended';

const Index = () => {
  const { profile } = useAuth();
  const navigate = useNavigate();
  // State for real dashboard stats
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // State for real recent buyers/orders
  const [recentBuyers, setRecentBuyers] = useState<any[]>([]);
  const [buyersLoading, setBuyersLoading] = useState(true);
  const [buyersError, setBuyersError] = useState<string | null>(null);
  const isMobile = useIsMobile();
  const [totalProducts, setTotalProducts] = useState<number>(0);
  const [totalEarn, setTotalEarn] = useState<number>(0);
  const [totalOrders, setTotalOrders] = useState<number>(0);
  const [pendingEarnings, setPendingEarnings] = useState<number>(0);
  const [productsMap, setProductsMap] = useState<Record<string, any>>({});

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      setStatsLoading(true);
      setStatsError(null);
      try {
        const stats = await fetchDashboardStats();
        setDashboardData(stats);
      } catch (err) {
        setStatsError('Failed to load statistics. Please try again later.');
      } finally {
        setStatsLoading(false);
      }
    };
    fetchStats();
  }, []);

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);
      try {
        const allProducts = await getEnhancedProducts();
        setProducts(allProducts);
        setTotalProducts(allProducts.length);
        // Build a map for productId -> product
        const map: Record<string, any> = {};
        allProducts.forEach((p: any) => { map[p.id] = p; });
        setProductsMap(map);
      } catch (err) {
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    fetchProducts();
  }, []);

  // Fetch recent buyers/orders for logged-in user
  useEffect(() => {
    const fetchBuyers = async () => {
      if (!profile?.id) return;
      setBuyersLoading(true);
      setBuyersError(null);
      try {
        const orders = await fetchOrdersByAffiliateWorkerId(profile.id);
        // Map orders to BuyersList format
        const buyers = orders.map((order: any) => {
          // Get product name from order_items[0] if available
          let productName = '';
          if (order.order_items && order.order_items[0]) {
            const prod = productsMap[order.order_items[0].product_id];
            productName = prod ? prod.title : order.order_items[0].product_id;
          }
          return {
            id: order.id,
            name: order.customer_name,
            phone: order.customer_phone,
            product: productName,
            date: order.order_date || order.created_at,
            amount: order.total,
            commission: order.affiliate_earnings,
            status: order.status,
          };
        });
        setRecentBuyers(buyers);
      } catch (err) {
        setBuyersError('Failed to load recent buyers. Please try again later.');
      } finally {
        setBuyersLoading(false);
      }
    };
    if (profile?.id && Object.keys(productsMap).length > 0) fetchBuyers();
  }, [profile?.id, productsMap]);

  // Fetch user's total earnings
  useEffect(() => {
    const fetchTotalEarn = async () => {
      if (!profile?.id) return;
      try {
        const earn = await getUserTotalEarnings(profile.id);
        setTotalEarn(earn);
      } catch {}
    };
    if (profile?.id) fetchTotalEarn();
  }, [profile?.id]);

  // Fetch user's total orders
  useEffect(() => {
    const fetchTotalOrders = async () => {
      if (!profile?.id) return;
      try {
        const orders = await fetchOrdersByAffiliateWorkerId(profile.id);
        setTotalOrders(orders.length);
      } catch {}
    };
    if (profile?.id) fetchTotalOrders();
  }, [profile?.id]);

  // Fetch user's pending earnings
  useEffect(() => {
    const fetchPendingEarnings = async () => {
      if (!profile?.id) return;
      try {
        const pending = await getUserPendingPayments(profile.id);
        setPendingEarnings(pending);
      } catch {}
    };
    if (profile?.id) fetchPendingEarnings();
  }, [profile?.id]);

  // Get user's first name for welcome message
  const getFirstName = () => {
    if (profile?.name) {
      return profile.name.split(' ')[0];
    }
    return 'there';
  };

  return (
    <Layout>
      {/* Welcome section */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Welcome back, {getFirstName()} 👋
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Here's what's happening with your affiliate account today.
        </p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        {statsLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="h-[100px] animate-pulse" />
          ))
        ) : statsError ? (
          <div className="col-span-4 text-center text-red-500">{statsError}</div>
        ) : dashboardData ? (
          <>
            <StatsCard
              icon={<Package2 size={20} />}
              title="Total Orders"
              value={totalOrders}
              change={null}
              className="animate-delay-1"
            />
            <StatsCard
              icon={<DollarSign size={20} />}
              title="Pending Earnings"
              value={formatCurrencyDa(pendingEarnings)}
              change={null}
              className="animate-delay-2"
              iconClassName="bg-warning/10 text-warning"
            />
            <StatsCard
              icon={<DollarSign size={20} />}
              title="Total Earn"
              value={formatCurrencyDa(totalEarn)}
              change={null}
              className="animate-delay-3"
              iconClassName="bg-success/10 text-success"
            />
            <StatsCard
              icon={<MousePointer size={20} />}
              title="Conversion Rate"
              value={`${dashboardData.conversionRate?.toFixed(1) || 0}%`}
              change={null}
              className="animate-delay-4"
              iconClassName="bg-accent/10 text-accent"
            />
          </>
        ) : null}
      </div>

      {/* Earnings Chart */}
      <div className="mb-8">
        <EarningsChart userId={profile?.id} />
      </div>

      {/* Products section */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4 sm:mb-6">
          <h2 className="text-lg sm:text-xl font-semibold">Top Products</h2>
          <Button
            variant="outline"
            size="sm"
            className="w-full sm:w-auto text-xs sm:text-sm"
            onClick={() => navigate('/products')}
          >
            <Plus className="mr-1 sm:mr-2 h-4 w-4" />
            View All Products
          </Button>
        </div>
        {loading ? (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="h-[300px] animate-pulse">
                <div className="h-full flex flex-col">
                  <div className="h-[180px] bg-muted rounded-t-lg"></div>
                  <div className="p-4 flex-1 flex flex-col gap-2">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-4 bg-muted rounded w-1/2"></div>
                    <div className="mt-auto h-8 bg-muted rounded"></div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500">{error}</p>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No products available.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
            {products.slice(0, 4).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>

      {/* Recent buyers */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4 sm:mb-6">
          <h2 className="text-lg sm:text-xl font-semibold">Recent Buyers</h2>
          <Button variant="outline" size="sm" className="w-full sm:w-auto text-xs sm:text-sm" onClick={() => navigate('/buyers')}>
            See All
          </Button>
        </div>
        <div className="overflow-x-auto -mx-4 sm:mx-0">
          {buyersLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : buyersError ? (
            <div className="text-center text-red-500 py-8">{buyersError}</div>
          ) : (
            <BuyersList buyers={recentBuyers} onViewOrder={() => navigate('/buyers')} />
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Index;
