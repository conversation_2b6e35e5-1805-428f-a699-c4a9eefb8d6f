import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { 
  fetchUserFavorites, 
  addToFavorites as apiAddToFavorites, 
  removeFromFavorites as apiRemoveFromFavorites,
  getUserFavoriteIds
} from '@/lib/api';
import { toast } from 'sonner';
import type { Product } from '@/types';

interface FavoritesContextType {
  favorites: Product[];
  favoriteIds: Set<string>;
  loading: boolean;
  addToFavorites: (product: Product) => Promise<boolean>;
  removeFromFavorites: (productId: string) => Promise<boolean>;
  isFavorited: (productId: string) => boolean;
  refreshFavorites: () => Promise<void>;
  clearFavorites: () => void;
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

export function useFavorites() {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
}

interface FavoritesProviderProps {
  children: React.ReactNode;
}

export function FavoritesProvider({ children }: FavoritesProviderProps) {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState<Product[]>([]);
  const [favoriteIds, setFavoriteIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  // Fetch user's favorites when user changes
  const refreshFavorites = useCallback(async () => {
    if (!user) {
      setFavorites([]);
      setFavoriteIds(new Set());
      return;
    }

    setLoading(true);
    try {
      const [favoritesData, favoriteIdsData] = await Promise.all([
        fetchUserFavorites(),
        getUserFavoriteIds()
      ]);
      
      setFavorites(favoritesData);
      setFavoriteIds(new Set(favoriteIdsData));
    } catch (error) {
      console.error('Error fetching favorites:', error);
      toast.error('Failed to load favorites');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load favorites when user changes
  useEffect(() => {
    refreshFavorites();
  }, [refreshFavorites]);

  // Clear favorites when user logs out
  useEffect(() => {
    if (!user) {
      clearFavorites();
    }
  }, [user]);

  // Add product to favorites with optimistic updates
  const addToFavorites = useCallback(async (product: Product): Promise<boolean> => {
    if (!user) {
      toast.error('Please log in to add favorites');
      return false;
    }

    const productId = product.id.toString();

    // Optimistic update
    setFavoriteIds(prev => new Set([...prev, productId]));
    setFavorites(prev => [product, ...prev]);

    try {
      const success = await apiAddToFavorites(productId);
      if (success) {
        toast.success('Added to favorites');
        return true;
      } else {
        // Revert optimistic update
        setFavoriteIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(productId);
          return newSet;
        });
        setFavorites(prev => prev.filter(fav => fav.id.toString() !== productId));
        toast.error('Failed to add to favorites');
        return false;
      }
    } catch (error) {
      // Revert optimistic update
      setFavoriteIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
      setFavorites(prev => prev.filter(fav => fav.id.toString() !== productId));
      toast.error('Failed to add to favorites');
      return false;
    }
  }, [user]);

  // Remove product from favorites with optimistic updates
  const removeFromFavorites = useCallback(async (productId: string): Promise<boolean> => {
    if (!user) {
      toast.error('Please log in to manage favorites');
      return false;
    }

    // Store the removed product for potential rollback
    const removedProduct = favorites.find(fav => fav.id.toString() === productId);

    // Optimistic update
    setFavoriteIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(productId);
      return newSet;
    });
    setFavorites(prev => prev.filter(fav => fav.id.toString() !== productId));

    try {
      const success = await apiRemoveFromFavorites(productId);
      if (success) {
        toast.success('Removed from favorites');
        return true;
      } else {
        // Revert optimistic update
        setFavoriteIds(prev => new Set([...prev, productId]));
        if (removedProduct) {
          setFavorites(prev => [removedProduct, ...prev]);
        }
        toast.error('Failed to remove from favorites');
        return false;
      }
    } catch (error) {
      // Revert optimistic update
      setFavoriteIds(prev => new Set([...prev, productId]));
      if (removedProduct) {
        setFavorites(prev => [removedProduct, ...prev]);
      }
      toast.error('Failed to remove from favorites');
      return false;
    }
  }, [user, favorites]);

  // Check if product is favorited
  const isFavorited = useCallback((productId: string): boolean => {
    return favoriteIds.has(productId);
  }, [favoriteIds]);

  // Clear all favorites (for logout)
  const clearFavorites = useCallback(() => {
    setFavorites([]);
    setFavoriteIds(new Set());
  }, []);

  const value = {
    favorites,
    favoriteIds,
    loading,
    addToFavorites,
    removeFromFavorites,
    isFavorited,
    refreshFavorites,
    clearFavorites,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
}
