import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate, formatCurrency, formatCurrencyDa } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface Buyer {
  id: string;
  name: string;
  phone: string;
  product: string;
  date: string;
  amount: number;
  commission: number;
  status: 'pending' | 'confirmed' | 'paid';
}

interface BuyersListProps {
  buyers: Buyer[];
  onViewOrder?: (orderId: string) => void;
}

const getBadgeVariant = (status: string) => {
  switch (status) {
    case 'pending':
      return 'outline';
    case 'confirmed':
      return 'secondary';
    case 'paid':
      return 'success';
    default:
      return 'outline';
  }
};

const BuyersList = ({ buyers, onViewOrder }: BuyersListProps) => {
  const isMobile = useIsMobile();

  if (buyers.length === 0) {
    return (
      <div className="text-center py-8 sm:py-10">
        <p className="text-muted-foreground text-sm sm:text-base">No buyers found</p>
        <Button className="mt-4 text-xs sm:text-sm">Add Your First Buyer</Button>
      </div>
    );
  }

  return (
    <div className="rounded-md border overflow-hidden">
      <Table className="w-full text-xs sm:text-sm">
        <TableHeader>
          <TableRow>
            <TableHead className="whitespace-nowrap">Buyer</TableHead>
            {!isMobile && <TableHead className="whitespace-nowrap">Date</TableHead>}
            <TableHead className="whitespace-nowrap">Amount</TableHead>
            <TableHead className="whitespace-nowrap">Commission</TableHead>
            <TableHead className="whitespace-nowrap">Status</TableHead>
            {!isMobile && <TableHead className="text-right whitespace-nowrap">Action</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {buyers.map((buyer) => (
            <TableRow key={buyer.id} className="opacity-0 animate-fade-in">
              <TableCell className="whitespace-nowrap">
                <div>
                  <p className="font-medium">{buyer.name}</p>
                  {!isMobile && <p className="text-xs text-muted-foreground">{buyer.phone}</p>}
                </div>
              </TableCell>
              {!isMobile && <TableCell className="whitespace-nowrap">{formatDate(buyer.date)}</TableCell>}
              <TableCell className="whitespace-nowrap">{formatCurrencyDa(buyer.amount)}</TableCell>
              <TableCell className="text-success whitespace-nowrap">
                {formatCurrencyDa(buyer.commission)}
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Badge className="capitalize text-[10px] sm:text-xs">
                  {buyer.status}
                </Badge>
              </TableCell>
              {!isMobile && (
                <TableCell className="text-right whitespace-nowrap">
                  <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={() => onViewOrder && onViewOrder(buyer.id)}>
                    View
                  </Button>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default BuyersList;
