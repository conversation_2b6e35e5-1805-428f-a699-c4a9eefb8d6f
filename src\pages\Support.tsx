
import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { toast } from 'sonner';
import { Search, HelpCircle, Book, MessageCircle, PhoneCall } from 'lucide-react';

const Support = () => {
  const [searchQuery, setSearchQuery] = useState('');
  
  const faqData = [
    {
      question: "How do I earn commissions?",
      answer: "You earn commissions when someone makes a purchase using your unique affiliate link. The commission is calculated as a percentage of the product's price. You'll see the earnings in your dashboard after the purchase is confirmed."
    },
    {
      question: "When do I get paid?",
      answer: "Commissions are paid on a monthly basis, typically on the 15th of each month. You must reach a minimum threshold of $50 to receive a payout. Payments are made via PayPal, bank transfer, or other methods specified in your profile settings."
    },
    {
      question: "How do I create affiliate links?",
      answer: "To create an affiliate link, browse the Products section and click on the 'Copy Link' button for any product you want to promote. This generates a unique link that tracks back to your account."
    },
    {
      question: "What is the cookie duration?",
      answer: "The cookie duration is 30 days. This means that if someone clicks on your affiliate link and makes a purchase within 30 days, you will receive the commission, even if they didn't buy immediately after clicking."
    },
    {
      question: "Can I promote products on social media?",
      answer: "Yes, you can promote products on any platform including social media, your blog, email newsletters, or websites. Just make sure you're following the terms of service of each platform regarding promotional content."
    },
    {
      question: "How do I track my performance?",
      answer: "Your performance metrics are available in your dashboard. You can view detailed statistics including clicks, conversions, and earnings. Use the Earnings page for a comprehensive breakdown of your commission history."
    },
  ];
  
  const filteredFAQs = searchQuery 
    ? faqData.filter(faq => 
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      ) 
    : faqData;
  
  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success('Your support request has been submitted. We will get back to you shortly.');
    
    // Reset form (in real app would use a form ref)
    const form = e.target as HTMLFormElement;
    form.reset();
  };

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-1">Support & Help Center</h1>
        <p className="text-muted-foreground">
          Find answers to your questions and get help
        </p>
      </div>
      
      <div className="relative mb-8">
        <div className="flex items-center p-8 rounded-lg bg-gradient-to-r from-primary/20 to-accent/20 text-center flex-col">
          <HelpCircle className="h-12 w-12 text-primary mb-4" />
          <h2 className="text-2xl font-bold mb-2">How can we help you today?</h2>
          <p className="text-muted-foreground mb-4 max-w-lg">
            Search our knowledge base or browse through our frequently asked questions
          </p>
          <div className="relative w-full max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input 
              placeholder="Search for answers..." 
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <Card className="hover:shadow-md transition-shadow animate-fade-in">
          <CardHeader className="text-center">
            <Book className="h-8 w-8 mx-auto mb-2 text-primary" />
            <CardTitle>Knowledge Base</CardTitle>
            <CardDescription>Browse our detailed guides</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button variant="outline">View Articles</Button>
          </CardFooter>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow animate-fade-in animate-delay-1">
          <CardHeader className="text-center">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 text-primary" />
            <CardTitle>Live Chat</CardTitle>
            <CardDescription>Chat with our support team</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button variant="outline">Start Chat</Button>
          </CardFooter>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow animate-fade-in animate-delay-2">
          <CardHeader className="text-center">
            <PhoneCall className="h-8 w-8 mx-auto mb-2 text-primary" />
            <CardTitle>Phone Support</CardTitle>
            <CardDescription>Call us directly</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button variant="outline">Call Now</Button>
          </CardFooter>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 className="text-xl font-bold mb-4">Frequently Asked Questions</h2>
          
          {filteredFAQs.length > 0 ? (
            <Accordion type="single" collapsible className="w-full">
              {filteredFAQs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger>{faq.question}</AccordionTrigger>
                  <AccordionContent>
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <p className="text-muted-foreground">No FAQ matches your search. Try different keywords.</p>
          )}
        </div>
        
        <div>
          <h2 className="text-xl font-bold mb-4">Contact Support</h2>
          <Card>
            <CardContent className="pt-6">
              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input id="name" name="name" required />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" name="email" type="email" required />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input id="subject" name="subject" required />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Input id="message" name="message" className="min-h-[100px]" required />
                </div>
                
                <Button type="submit" className="w-full">Submit Request</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Support;
