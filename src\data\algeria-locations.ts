// Algeria wilayas and cities data

export interface City {
  name: string;
  id: number;
}

export interface Wilaya {
  id: number;
  name: string;
  cities: City[];
  homeDeliveryPrice: number;
  deskDeliveryPrice: number;
}

export const algeriaWilayas: Wilaya[] = [
  {
    id: 1,
    name: "Adrar",
    homeDeliveryPrice: 800,
    deskDeliveryPrice: 600,
    cities: [
      { id: 1, name: "Adrar" },
      { id: 2, name: "Reggane" },
      { id: 3, name: "Timimoun" }
    ]
  },
  {
    id: 2,
    name: "Chlef",
    homeDeliveryPrice: 600,
    deskDeliveryPrice: 400,
    cities: [
      { id: 1, name: "<PERSON>le<PERSON>" },
      { id: 2, name: "<PERSON><PERSON><PERSON>" },
      { id: 3, name: "Boukadir" }
    ]
  },
  {
    id: 3,
    name: "Laghouat",
    homeDeliveryPrice: 700,
    deskDeliveryPrice: 500,
    cities: [
      { id: 1, name: "Laghouat" },
      { id: 2, name: "<PERSON><PERSON><PERSON>" },
      { id: 3, name: "<PERSON><PERSON>" }
    ]
  },
  {
    id: 4,
    name: "Oum El Bouaghi",
    homeDeliveryPrice: 650,
    deskDeliveryPrice: 450,
    cities: [
      { id: 1, name: "Oum El Bouaghi" },
      { id: 2, name: "Aïn Beïda" },
      { id: 3, name: "Aïn M'lila" }
    ]
  },
  {
    id: 5,
    name: "Batna",
    homeDeliveryPrice: 600,
    deskDeliveryPrice: 400,
    cities: [
      { id: 1, name: "Batna" },
      { id: 2, name: "Barika" },
      { id: 3, name: "Arris" }
    ]
  },
  {
    id: 6,
    name: "Béjaïa",
    homeDeliveryPrice: 550,
    deskDeliveryPrice: 350,
    cities: [
      { id: 1, name: "Béjaïa" },
      { id: 2, name: "Akbou" },
      { id: 3, name: "Souk El Ténine" }
    ]
  },
  {
    id: 7,
    name: "Biskra",
    homeDeliveryPrice: 650,
    deskDeliveryPrice: 450,
    cities: [
      { id: 1, name: "Biskra" },
      { id: 2, name: "Ouled Djellal" },
      { id: 3, name: "Tolga" }
    ]
  },
  {
    id: 8,
    name: "Béchar",
    homeDeliveryPrice: 800,
    deskDeliveryPrice: 600,
    cities: [
      { id: 1, name: "Béchar" },
      { id: 2, name: "Abadla" },
      { id: 3, name: "Beni Abbès" }
    ]
  },
  {
    id: 9,
    name: "Blida",
    homeDeliveryPrice: 500,
    deskDeliveryPrice: 300,
    cities: [
      { id: 1, name: "Blida" },
      { id: 2, name: "Boufarik" },
      { id: 3, name: "Mouzaïa" }
    ]
  },
  {
    id: 10,
    name: "Bouira",
    homeDeliveryPrice: 550,
    deskDeliveryPrice: 350,
    cities: [
      { id: 1, name: "Bouira" },
      { id: 2, name: "Lakhdaria" },
      { id: 3, name: "Sour El-Ghozlane" }
    ]
  },
  {
    id: 16,
    name: "Alger",
    homeDeliveryPrice: 500,
    deskDeliveryPrice: 300,
    cities: [
      { id: 1, name: "Alger Centre" },
      { id: 2, name: "Bab Ezzouar" },
      { id: 3, name: "Bir Mourad Raïs" },
      { id: 4, name: "El Harrach" },
      { id: 5, name: "Dar El Beïda" },
      { id: 6, name: "Hussein Dey" }
    ]
  },
  {
    id: 31,
    name: "Oran",
    homeDeliveryPrice: 550,
    deskDeliveryPrice: 350,
    cities: [
      { id: 1, name: "Oran" },
      { id: 2, name: "Arzew" },
      { id: 3, name: "Bir El Djir" },
      { id: 4, name: "Es Senia" }
    ]
  },
  {
    id: 23,
    name: "Annaba",
    homeDeliveryPrice: 600,
    deskDeliveryPrice: 400,
    cities: [
      { id: 1, name: "Annaba" },
      { id: 2, name: "El Bouni" },
      { id: 3, name: "Berrahal" }
    ]
  },
  {
    id: 25,
    name: "Constantine",
    homeDeliveryPrice: 600,
    deskDeliveryPrice: 400,
    cities: [
      { id: 1, name: "Constantine" },
      { id: 2, name: "El Khroub" },
      { id: 3, name: "Hamma Bouziane" }
    ]
  }
];

// Helper function to get cities by wilaya ID
export const getCitiesByWilayaId = (wilayaId: number): City[] => {
  const wilaya = algeriaWilayas.find(w => w.id === wilayaId);
  return wilaya ? wilaya.cities : [];
};

// Helper function to get delivery prices by wilaya ID
export const getDeliveryPricesByWilayaId = (wilayaId: number): { home: number, desk: number } => {
  const wilaya = algeriaWilayas.find(w => w.id === wilayaId);
  return wilaya 
    ? { home: wilaya.homeDeliveryPrice, desk: wilaya.deskDeliveryPrice }
    : { home: 0, desk: 0 };
};
