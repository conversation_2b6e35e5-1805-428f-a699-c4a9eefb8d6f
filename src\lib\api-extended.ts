// Extended API functions for Supabase backend
import { supabase } from './supabase';
import type { 
  AffiliateWorker, 
  Order, 
  OrderItem, 
  DeliveryLocation, 
  Payment,
  DashboardStats,
  CreateOrderForm
} from '../types';

// Utility function to handle Supabase errors
function handleSupabaseError(error: any, operation: string) {
  console.error(`Error in ${operation}:`, error);
  throw new Error(error.message || `Failed to ${operation}`);
}

// ===== AFFILIATE WORKERS API =====

// Note: Affiliate workers are now managed through the profiles table (auth.users)
// These functions are deprecated and replaced by user profile management

// Function to fetch all users with affiliate role (replaces fetchAffiliateWorkers)
export async function fetchAffiliateUsers(): Promise<AffiliateWorker[]> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, phone, role')
      .eq('role', 'affiliate')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch affiliate users');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch a single user profile by ID (replaces fetchAffiliateWorkerById)
export async function fetchUserProfileById(id: string): Promise<AffiliateWorker> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, phone, role')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch user profile by ID');
    throw error; // Re-throw to allow caller to handle
  }
}

// ===== ORDERS API =====

// Function to fetch all orders with related data
export async function fetchOrders(): Promise<Order[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers!inner(name, phone)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Fetch user profiles separately for affiliate workers
    const orderIds = data.map(order => order.id);
    const userIds = data.map(order => order.affiliate_worker_id).filter(Boolean);

    let profiles: any[] = [];
    if (userIds.length > 0) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, name, phone, role')
        .in('id', userIds);

      if (!profileError) {
        profiles = profileData || [];
      }
    }

    return data.map((order: any) => {
      const profile = profiles.find(p => p.id === order.affiliate_worker_id);
      return {
        ...order,
        customer_name: order.customers.name,
        customer_phone: order.customers.phone,
        affiliate_worker: profile ? {
          name: profile.name,
          phone: profile.phone,
          role: profile.role
        } : null
      };
    });
  } catch (error) {
    handleSupabaseError(error, 'fetch orders');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch a single order by ID with all related data
export async function fetchOrderById(id: string): Promise<Order> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers!inner(name, phone, location, address),
        order_items(
          *,
          products(title, main_image)
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    // Fetch user profile separately if affiliate_worker_id exists
    let profile = null;
    if (data.affiliate_worker_id) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, name, phone, role')
        .eq('id', data.affiliate_worker_id)
        .single();

      if (!profileError && profileData) {
        profile = profileData;
      }
    }

    return {
      ...data,
      customer_name: data.customers.name,
      customer_phone: data.customers.phone,
      customer_location: data.customers.location,
      customer_address: data.customers.address,
      affiliate_worker: profile ? {
        name: profile.name,
        phone: profile.phone,
        role: profile.role
      } : null,
      order_items: data.order_items.map((item: any) => ({
        ...item,
        product_name: item.products.title,
        product_image: item.products.main_image
      }))
    };
  } catch (error) {
    handleSupabaseError(error, `fetch order with ID ${id}`);
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch orders by affiliate worker ID
export async function fetchOrdersByAffiliateWorkerId(affiliateWorkerId: string): Promise<Order[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers!inner(name, phone),
        order_items(
          id,
          quantity,
          price,
          affiliate_earnings
        )
      `)
      .eq('affiliate_worker_id', affiliateWorkerId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Fetch user profile separately for the affiliate worker
    let profile = null;
    if (affiliateWorkerId) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, name, phone, role')
        .eq('id', affiliateWorkerId)
        .single();

      if (!profileError && profileData) {
        profile = profileData;
      }
    }

    return data.map((order: any) => {
      // Calculate total affiliate earnings for this order
      const totalEarnings = order.order_items?.reduce((sum: number, item: any) => {
        return sum + (item.affiliate_earnings || 0);
      }, 0) || 0;

      return {
        ...order,
        customer_name: order.customers.name,
        customer_phone: order.customers.phone,
        affiliate_earnings: totalEarnings,
        affiliate_worker: profile ? {
          name: profile.name,
          phone: profile.phone,
          role: profile.role
        } : null
      };
    });
  } catch (error) {
    handleSupabaseError(error, 'fetch orders by affiliate worker ID');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new order
export async function createOrder(orderData: CreateOrderForm): Promise<Order> {
  try {
    // Calculate total from items
    const total = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) + orderData.shipping_cost;

    // Create the order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        customer_id: orderData.customer_id,
        affiliate_worker_id: orderData.affiliate_worker_id,
        order_date: new Date().toISOString(),
        status: 'pending',
        total,
        items_count: orderData.items.reduce((sum, item) => sum + item.quantity, 0),
        payment_method: orderData.payment_method,
        shipping_cost: orderData.shipping_cost,
        notes: orderData.notes
      })
      .select()
      .single();

    if (orderError) throw orderError;

    // Create order items
    const orderItems = orderData.items.map(item => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      price: item.price,
      affiliate_earnings: item.affiliate_earnings || 0
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) throw itemsError;

    return await fetchOrderById(order.id);
  } catch (error) {
    handleSupabaseError(error, 'create order');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update order status
export async function updateOrderStatus(id: string, status: Order['status']): Promise<Order> {
  try {
    const { error } = await supabase
      .from('orders')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) throw error;
    return await fetchOrderById(id);
  } catch (error) {
    handleSupabaseError(error, 'update order status');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to delete an order (only if status is pending)
export async function deleteOrder(id: string, affiliateWorkerId: string): Promise<void> {
  try {
    // First, check if the order exists and belongs to the affiliate worker
    const { data: order, error: fetchError } = await supabase
      .from('orders')
      .select('id, status, affiliate_worker_id')
      .eq('id', id)
      .eq('affiliate_worker_id', affiliateWorkerId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        throw new Error('Order not found or you do not have permission to delete this order');
      }
      throw fetchError;
    }

    // Check if order status is pending
    if (order.status !== 'pending') {
      throw new Error('Only pending orders can be deleted');
    }

    // Delete order items first (due to foreign key constraint)
    const { error: itemsError } = await supabase
      .from('order_items')
      .delete()
      .eq('order_id', id);

    if (itemsError) throw itemsError;

    // Delete the order
    const { error: orderError } = await supabase
      .from('orders')
      .delete()
      .eq('id', id)
      .eq('affiliate_worker_id', affiliateWorkerId);

    if (orderError) throw orderError;
  } catch (error) {
    handleSupabaseError(error, 'delete order');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== DELIVERY LOCATIONS API =====

// Function to fetch all delivery locations
export async function fetchDeliveryLocations(): Promise<DeliveryLocation[]> {
  try {
    const { data, error } = await supabase
      .from('delivery_locations')
      .select('*')
      .order('wilaya_code');

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch delivery locations');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update delivery location pricing
export async function updateDeliveryLocation(id: string, locationData: Partial<DeliveryLocation>): Promise<DeliveryLocation> {
  try {
    const { data, error } = await supabase
      .from('delivery_locations')
      .update({
        ...locationData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update delivery location');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update delivery location by wilaya code
export async function updateDeliveryLocationByWilayaCode(wilayaCode: number, locationData: Partial<DeliveryLocation>): Promise<DeliveryLocation> {
  try {
    const { data, error } = await supabase
      .from('delivery_locations')
      .update({
        ...locationData,
        updated_at: new Date().toISOString()
      })
      .eq('wilaya_code', wilayaCode)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update delivery location by wilaya code');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to batch update multiple delivery locations
export async function batchUpdateDeliveryLocations(updates: Array<{ wilayaCode: number; data: Partial<DeliveryLocation> }>): Promise<DeliveryLocation[]> {
  try {
    const results: DeliveryLocation[] = [];

    // Process updates in batches to avoid overwhelming the database
    for (const update of updates) {
      const result = await updateDeliveryLocationByWilayaCode(update.wilayaCode, update.data);
      results.push(result);
    }

    return results;
  } catch (error) {
    handleSupabaseError(error, 'batch update delivery locations');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// ===== PAYMENTS API =====

// Function to fetch all payments
export async function fetchPayments(): Promise<Payment[]> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Fetch user profiles separately
    const userIds = data.map(payment => payment.affiliate_worker_id).filter(Boolean);

    let profiles: any[] = [];
    if (userIds.length > 0) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, name, phone, role')
        .in('id', userIds);

      if (!profileError) {
        profiles = profileData || [];
      }
    }

    return data.map((payment: any) => {
      const profile = profiles.find(p => p.id === payment.affiliate_worker_id);
      return {
        ...payment,
        affiliate_worker: profile ? {
          name: profile.name,
          phone: profile.phone,
          role: profile.role
        } : null
      };
    });
  } catch (error) {
    handleSupabaseError(error, 'fetch payments');
    return []; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to create a new payment
export async function createPayment(affiliateWorkerId: string, amount: number): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .insert({
        affiliate_worker_id: affiliateWorkerId,
        amount,
        payment_date: new Date().toISOString(),
        status: 'pending'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'create payment');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to update payment status
export async function updatePaymentStatus(id: string, status: Payment['status']): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'update payment status');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

// Function to fetch payments for a specific user
export async function fetchUserPayments(userId: string): Promise<Payment[]> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('affiliate_worker_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'fetch user payments');
    return [];
  }
}

// Function to create a payment request
export async function createPaymentRequest(userId: string, amount: number, notes?: string): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .insert({
        affiliate_worker_id: userId,
        amount,
        status: 'pending',
        notes,
        payment_date: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    handleSupabaseError(error, 'create payment request');
    throw error;
  }
}

// Function to get user's total earnings from order items
export async function getUserTotalEarnings(userId: string): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('order_items')
      .select('affiliate_earnings, orders!inner(affiliate_worker_id, status)')
      .eq('orders.affiliate_worker_id', userId)
      .eq('orders.status', 'delivered');

    if (error) throw error;

    return data.reduce((total, item) => total + (item.affiliate_earnings || 0), 0);
  } catch (error) {
    handleSupabaseError(error, 'get user total earnings');
    return 0;
  }
}

// Function to get user's pending payment amount
export async function getUserPendingPayments(userId: string): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('amount')
      .eq('affiliate_worker_id', userId)
      .eq('status', 'pending');

    if (error) throw error;

    return data.reduce((total, payment) => total + payment.amount, 0);
  } catch (error) {
    handleSupabaseError(error, 'get user pending payments');
    return 0;
  }
}

// Function to get user's paid payment amount
export async function getUserPaidPayments(userId: string): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('amount')
      .eq('affiliate_worker_id', userId)
      .eq('status', 'paid');

    if (error) throw error;

    return data.reduce((total, payment) => total + payment.amount, 0);
  } catch (error) {
    handleSupabaseError(error, 'get user paid payments');
    return 0;
  }
}

// Function to delete a payment request (only for pending requests)
export async function deletePaymentRequest(paymentId: string, userId: string): Promise<void> {
  try {
    // First verify the payment belongs to the user and is pending
    const { data: payment, error: fetchError } = await supabase
      .from('payments')
      .select('status, affiliate_worker_id')
      .eq('id', paymentId)
      .single();

    if (fetchError) throw fetchError;

    if (!payment) {
      throw new Error('Payment request not found');
    }

    if (payment.affiliate_worker_id !== userId) {
      throw new Error('Unauthorized: Payment request does not belong to this user');
    }

    if (payment.status !== 'pending') {
      throw new Error('Can only delete pending payment requests');
    }

    // Delete the payment request
    const { error: deleteError } = await supabase
      .from('payments')
      .delete()
      .eq('id', paymentId);

    if (deleteError) throw deleteError;
  } catch (error) {
    handleSupabaseError(error, 'delete payment request');
    throw error;
  }
}

// ===== DASHBOARD STATS API =====

// Function to get dashboard statistics
export async function fetchDashboardStats(): Promise<DashboardStats> {
  try {
    // Get total revenue from orders
    const { data: revenueData } = await supabase
      .from('orders')
      .select('total')
      .eq('status', 'delivered');

    const totalRevenue = revenueData?.reduce((sum, order) => sum + order.total, 0) || 0;

    // Get total orders count
    const { count: totalOrders } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true });

    // Get total customers count
    const { count: totalCustomers } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true });

    // Calculate conversion rate (delivered orders / total orders)
    const { count: deliveredOrders } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'delivered');

    const conversionRate = totalOrders ? (deliveredOrders || 0) / totalOrders * 100 : 0;

    return {
      totalRevenue,
      totalOrders: totalOrders || 0,
      totalCustomers: totalCustomers || 0,
      conversionRate,
      revenueGrowth: 0, // TODO: Calculate based on previous period
      ordersGrowth: 0,  // TODO: Calculate based on previous period
      customersGrowth: 0, // TODO: Calculate based on previous period
      conversionGrowth: 0 // TODO: Calculate based on previous period
    };
  } catch (error) {
    handleSupabaseError(error, 'fetch dashboard stats');
    throw error; // This line will never be reached due to the throw in handleSupabaseError
  }
}

/**
 * Fetch earnings and referrals grouped by period (monthly or weekly) for a user
 * @param userId - affiliate worker id
 * @param period - 'monthly' | 'weekly'
 * @returns Array of { name, earnings, referrals }
 */
export async function fetchEarningsChartDataByUser(userId: string, period: 'monthly' | 'weekly') {
  if (!userId) return [];
  // Determine date range (last 6 months or last 4 weeks)
  const now = new Date();
  let startDate;
  let groupFormat;
  if (period === 'monthly') {
    startDate = new Date(now.getFullYear(), now.getMonth() - 5, 1); // 6 months ago
    groupFormat = 'YYYY-MM';
  } else {
    // 4 weeks ago (start of week)
    startDate = new Date(now);
    startDate.setDate(now.getDate() - 28);
    groupFormat = 'IYYY-IW'; // ISO week
  }
  // Query order_items joined with orders for this affiliate
  const { data, error } = await supabase
    .from('order_items')
    .select(`affiliate_earnings, created_at, orders!inner(id, affiliate_worker_id, status)`)
    .gte('created_at', startDate.toISOString())
    .eq('orders.affiliate_worker_id', userId)
    .eq('orders.status', 'delivered');
  if (error) return [];
  // Group and sum by period
  const groupMap = new Map();
  data.forEach(item => {
    const date = new Date(item.created_at);
    let key, label;
    if (period === 'monthly') {
      key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      label = date.toLocaleString('default', { month: 'short' });
    } else {
      // Get ISO week number
      const temp = new Date(date.getTime());
      temp.setHours(0,0,0,0);
      const dayNum = temp.getDay() || 7;
      temp.setDate(temp.getDate() + 4 - dayNum);
      const yearStart = new Date(temp.getFullYear(),0,1);
      const weekNum = Math.ceil((((temp.getTime() - yearStart.getTime()) / 86400000) + 1)/7);
      key = `${temp.getFullYear()}-W${weekNum}`;
      label = `Week ${weekNum}`;
    }
    if (!groupMap.has(key)) {
      groupMap.set(key, { name: label, earnings: 0, referrals: 0 });
    }
    const group = groupMap.get(key);
    group.earnings += item.affiliate_earnings || 0;
    group.referrals += 1;
  });
  // Sort by key (date order)
  const sorted = Array.from(groupMap.entries()).sort(([a], [b]) => a.localeCompare(b));
  // For monthly, show last 6 months; for weekly, last 4 weeks
  const result = sorted.slice(- (period === 'monthly' ? 6 : 4)).map(([, v]) => v);
  return result;
}
