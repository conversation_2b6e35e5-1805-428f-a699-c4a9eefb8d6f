import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { formatCurrencyDa, formatDate } from '@/lib/utils';
import { DollarSign, CreditCard, Clock, CheckCircle, XCircle, Plus, Banknote, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import StatsCard from '@/components/dashboard/StatsCard';
import {
  fetchUserPayments,
  createPaymentRequest,
  getUserTotalEarnings,
  getUserPendingPayments,
  getUserPaidPayments,
  deletePaymentRequest
} from '@/lib/api-extended';
import type { Payment } from '../types';



const Payments = () => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [paymentRequests, setPaymentRequests] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [requestAmount, setRequestAmount] = useState('');
  const [requestNotes, setRequestNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Real data from API
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [availableBalance, setAvailableBalance] = useState(0);
  const [pendingPayments, setPendingPayments] = useState(0);
  const [paidPayments, setPaidPayments] = useState(0);

  useEffect(() => {
    if (user?.id) {
      fetchPaymentRequests();
      fetchEarningsData();
    }
  }, [user?.id]);

  const fetchPaymentRequests = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const payments = await fetchUserPayments(user.id);
      setPaymentRequests(payments);
    } catch (error) {
      console.error('Error fetching payment requests:', error);
      toast.error('Failed to load payment requests');
    } finally {
      setLoading(false);
    }
  };

  const fetchEarningsData = async () => {
    if (!user?.id) return;

    try {
      const [earnings, pending, paid] = await Promise.all([
        getUserTotalEarnings(user.id),
        getUserPendingPayments(user.id),
        getUserPaidPayments(user.id)
      ]);

      setTotalEarnings(earnings);
      setPendingPayments(pending);
      setPaidPayments(paid);
      // Available balance = total earnings - pending payments - paid payments
      setAvailableBalance(earnings - pending - paid);
    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    }
  };

  const handleSubmitRequest = async () => {
    if (!user?.id) {
      toast.error('User not authenticated');
      return;
    }

    if (!requestAmount || parseFloat(requestAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    const amount = parseFloat(requestAmount);
    if (amount > availableBalance) {
      toast.error('Requested amount exceeds available balance');
      return;
    }

    try {
      setSubmitting(true);

      const newRequest = await createPaymentRequest(
        user.id,
        amount,
        requestNotes || undefined
      );

      setPaymentRequests(prev => [newRequest, ...prev]);
      setIsDialogOpen(false);
      setRequestAmount('');
      setRequestNotes('');

      // Update pending payments and available balance
      setPendingPayments(prev => prev + amount);
      setAvailableBalance(prev => prev - amount);

      toast.success('Payment request submitted successfully!');
    } catch (error) {
      console.error('Error submitting payment request:', error);
      toast.error('Failed to submit payment request');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteRequest = async (paymentId: string, amount: number) => {
    if (!user?.id) {
      toast.error('User not authenticated');
      return;
    }

    try {
      setDeletingId(paymentId);

      await deletePaymentRequest(paymentId, user.id);

      // Remove the payment from the list
      setPaymentRequests(prev => prev.filter(payment => payment.id !== paymentId));

      // Update the balances
      setPendingPayments(prev => prev - amount);
      setAvailableBalance(prev => prev + amount);

      toast.success('Payment request deleted successfully!');
    } catch (error) {
      console.error('Error deleting payment request:', error);
      toast.error('Failed to delete payment request');
    } finally {
      setDeletingId(null);
    }
  };

  const getStatusBadge = (status: Payment['status']) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case 'paid':
        return (
          <Badge variant="default" className="gap-1 bg-green-600 hover:bg-green-700">
            <CheckCircle className="h-3 w-3" />
            Paid
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className="gap-1">
            <XCircle className="h-3 w-3" />
            Cancelled
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-1">Payment Requests</h1>
        <p className="text-muted-foreground">
          Manage your affiliate earnings and payment requests
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <StatsCard
          icon={<CreditCard />}
          title="Available Balance"
          value={formatCurrencyDa(availableBalance)}
          iconClassName="bg-green-500/10 text-green-600"
        />

        <StatsCard
          icon={<Banknote />}
          title="Paid Payments"
          value={formatCurrencyDa(paidPayments)}
          iconClassName="bg-blue-500/10 text-blue-600"
        />

        <StatsCard
          icon={<Clock />}
          title="Pending Payments"
          value={formatCurrencyDa(pendingPayments)}
          iconClassName="bg-orange-500/10 text-orange-600"
        />
      </div>

      {/* Request Payment Button */}
      <div className="mb-6">
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Request Payment
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Request Payment</DialogTitle>
              <DialogDescription>
                Request a payment from your available balance of {formatCurrencyDa(availableBalance)}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="amount">Amount (Da)</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter amount"
                  value={requestAmount}
                  onChange={(e) => setRequestAmount(e.target.value)}
                  max={availableBalance}
                  min="1"
                />
                <p className="text-sm text-muted-foreground">
                  Maximum: {formatCurrencyDa(availableBalance)}
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any notes for this payment request..."
                  value={requestNotes}
                  onChange={(e) => setRequestNotes(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSubmitRequest}
                disabled={submitting}
              >
                {submitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Payment Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription>
            View all your payment requests and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Loading payment requests...</div>
            </div>
          ) : paymentRequests.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">No payment requests found</div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Request Date</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Notes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paymentRequests.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">
                        {formatCurrencyDa(payment.amount)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(payment.status)}
                      </TableCell>
                      <TableCell>
                        {formatDate(payment.created_at)}
                      </TableCell>
                      <TableCell>
                        {payment.payment_method || 'Not specified'}
                      </TableCell>
                      <TableCell>
                        {payment.notes || '-'}
                      </TableCell>
                      <TableCell className="text-right">
                        {payment.status === 'pending' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteRequest(payment.id, payment.amount)}
                            disabled={deletingId === payment.id}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            {deletingId === payment.id ? (
                              <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </Layout>
  );
};

export default Payments;
