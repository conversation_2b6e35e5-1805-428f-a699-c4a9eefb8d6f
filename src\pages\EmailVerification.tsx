import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Mail, ArrowRight, Loader2 } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

const EmailVerification = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    const handleEmailVerification = async () => {
      try {
        // Get the token and type from URL parameters
        const token = searchParams.get('token');
        const type = searchParams.get('type');

        // Check if we have the required parameters
        if (!token || type !== 'signup') {
          // If no token, check if user is already verified (automatic verification)
          const { data: { session } } = await supabase.auth.getSession();
          
          if (session?.user?.email_confirmed_at) {
            // Email is already confirmed (automatic verification happened)
            setIsSuccess(true);
            setIsVerifying(false);
            toast.success('Email verified successfully! You can now log in to your account.');
            return;
          } else {
            toast.error('Invalid verification link');
            setIsVerifying(false);
            return;
          }
        }

        // Try to verify the token manually
        const { error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: 'signup'
        });

        if (error) {
          console.error('Verification error:', error);
          toast.error('Email verification failed. Please try again or contact support.');
          setIsVerifying(false);
        } else {
          setIsSuccess(true);
          setIsVerifying(false);
          toast.success('Email verified successfully! You can now log in to your account.');
        }
      } catch (error) {
        console.error('Verification error:', error);
        toast.error('An unexpected error occurred during verification.');
        setIsVerifying(false);
      }
    };

    handleEmailVerification();
  }, [searchParams, navigate]);

  // Separate effect for countdown when success is true
  useEffect(() => {
    if (isSuccess) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            navigate('/login');
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isSuccess, navigate]);

  const handleGoToLogin = () => {
    navigate('/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-6">
      <Card className="w-full max-w-md border-0 shadow-none">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-6">
            <img
              src="/Logo.jpg"
              alt="Logo"
              className="w-16 h-16 rounded-full object-cover border border-border"
            />
          </div>
          <div className="p-3 rounded-lg mb-4">
            <p className="text-sm text-amber-800 text-center font-medium">
              🔒 Admin Panel
            </p>
          </div>
          
          {isVerifying ? (
            <>
              <div className="flex justify-center mb-4">
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-primary/10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-center">Verifying Email</CardTitle>
              <CardDescription className="text-center">
                Please wait while we verify your email address...
              </CardDescription>
            </>
          ) : isSuccess ? (
            <>
              <div className="flex justify-center mb-4">
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-green-100">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-center text-green-600">
                Email Verified!
              </CardTitle>
              <CardDescription className="text-center">
                Your email has been successfully verified. You can now log in to your account.
              </CardDescription>
            </>
          ) : (
            <>
              <div className="flex justify-center mb-4">
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-red-100">
                  <Mail className="h-8 w-8 text-red-600" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-center text-red-600">
                Verification Failed
              </CardTitle>
              <CardDescription className="text-center">
                We couldn't verify your email. Please try again or contact support.
              </CardDescription>
            </>
          )}
        </CardHeader>
        
        <CardContent className="space-y-4">
          {isSuccess && (
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                Redirecting to login page in {countdown} seconds...
              </p>
              <Button 
                onClick={handleGoToLogin} 
                className="w-full"
                variant="outline"
              >
                Go to Login Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
          
          {!isVerifying && !isSuccess && (
            <div className="text-center space-y-4">
              <Button 
                onClick={handleGoToLogin} 
                className="w-full"
              >
                Go to Login
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <p className="text-xs text-muted-foreground">
                If you're having trouble, please check your email for a new verification link or contact support.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailVerification; 