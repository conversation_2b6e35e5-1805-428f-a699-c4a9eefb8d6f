import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import {
  LayoutDashboard,
  Home,
  Users,
  Link as LinkIcon,
  DollarSign,
  CreditCard,
  User,
  LogOut,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  HelpCircle,
  Bell,
  X,
  Grid,
  Heart,
  Settings as SettingsIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetClose } from "@/components/ui/sheet";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface SidebarProps {
  className?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

const Sidebar = ({ className, isOpen = false, onClose }: SidebarProps) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const [collapsed, setCollapsed] = useState(false);

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success('Logged out successfully');
      navigate('/login');
    } catch (error) {
      toast.error('Error logging out');
    }
  };

  // Render mobile sidebar using Sheet component
  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="left" className="p-0 w-[280px] sm:w-[320px]">
          <div className="flex flex-col h-full bg-sidebar text-sidebar-foreground">
            {/* Mobile Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <div className="flex items-center gap-3">
                <img
                  src="/Logo.jpg"
                  alt="Logo"
                  className="w-10 h-10 rounded-full object-cover border border-border bg-white"
                />
                <span className="font-semibold text-lg">AffiMate</span>
              </div>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" className="text-muted-foreground">
                  <X size={18} />
                </Button>
              </SheetClose>
            </div>

            {/* Mobile Navigation Links */}
            <div className="flex-1 py-4 overflow-y-auto">
              <nav className="px-2 space-y-1">
                <MobileNavItem to="/" icon={<Home size={20} />} label="Dashboard" />
                <MobileNavItem to="/products" icon={<ShoppingBag size={20} />} label="Products" />
                <MobileNavItem to="/categories" icon={<Grid size={20} />} label="Categories" />
                <MobileNavItem to="/buyers" icon={<Users size={20} />} label="Buyers" />
                <MobileNavItem to="/payments" icon={<CreditCard size={20} />} label="Payments" />
                <MobileNavItem to="/favorites" icon={<Heart size={20} />} label="Favorites" />
                <MobileNavItem to="/profile" icon={<User size={20} />} label="My Profile" />
                <MobileNavItem to="/settings" icon={<SettingsIcon size={20} />} label="Settings" />
                <MobileNavItem to="/support" icon={<HelpCircle size={20} />} label="Support" />
              </nav>
            </div>

            {/* Mobile Footer */}
            <div className="p-4 border-t border-border">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-3 py-2 text-sm rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground justify-start gap-3"
              >
                <LogOut size={20} />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // Desktop sidebar
  return (
    <div className={cn(
      "relative h-screen flex flex-col bg-sidebar text-sidebar-foreground border-r border-border transition-all duration-300 hidden md:flex",
      collapsed ? "w-20" : "w-64",
      className
    )}>
      {/* Logo */}
      <div className="relative flex flex-col items-center justify-center p-4 border-b border-border">
        <div className="flex items-center justify-center w-full">
          <img
            src="/Logo.jpg"
            alt="Logo"
            className="w-10 h-10 rounded-full object-cover border border-border bg-white"
            style={{ maxWidth: '40px', maxHeight: '40px' }}
          />
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className={cn("absolute -right-3 top-1/2 -translate-y-1/2 bg-background border border-border rounded-full shadow-sm text-muted-foreground", collapsed && "rotate-180")}
        >
          {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
        </Button>
      </div>

      {/* Navigation Links */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="px-2 space-y-1">
          <NavItem to="/" icon={<Home size={20} />} label="Dashboard" collapsed={collapsed} />
          <NavItem to="/products" icon={<ShoppingBag size={20} />} label="Products" collapsed={collapsed} />
          <NavItem to="/categories" icon={<Grid size={20} />} label="Categories" collapsed={collapsed} />
          <NavItem to="/buyers" icon={<Users size={20} />} label="Buyers" collapsed={collapsed} />
          <NavItem to="/payments" icon={<CreditCard size={20} />} label="Payments" collapsed={collapsed} />
          <NavItem to="/favorites" icon={<Heart size={20} />} label="Favorites" collapsed={collapsed} />
          <NavItem to="/profile" icon={<User size={20} />} label="My Profile" collapsed={collapsed} />
          <NavItem to="/settings" icon={<SettingsIcon size={20} />} label="Settings" collapsed={collapsed} />
          <NavItem to="/support" icon={<HelpCircle size={20} />} label="Support" collapsed={collapsed} />
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <button
          onClick={handleLogout}
          className={cn(
            "flex items-center w-full px-3 py-2 text-sm rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
            !collapsed && "justify-start gap-3",
            collapsed && "justify-center"
          )}
        >
          <LogOut size={20} />
          {!collapsed && <span>Logout</span>}
        </button>
      </div>
    </div>
  );
};

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  collapsed: boolean;
}

const NavItem = ({ to, icon, label, collapsed }: NavItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
      to === window.location.pathname && "bg-sidebar-accent text-sidebar-accent-foreground",
      !collapsed && "justify-start gap-3",
      collapsed && "justify-center"
    )}
  >
    {icon}
    {!collapsed && <span>{label}</span>}
  </Link>
);

// Mobile navigation item component
interface MobileNavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
}

const MobileNavItem = ({ to, icon, label }: MobileNavItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center px-3 py-3 text-sm rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground justify-start gap-3",
      to === window.location.pathname && "bg-sidebar-accent text-sidebar-accent-foreground"
    )}
  >
    {icon}
    <span>{label}</span>
  </Link>
);

export default Sidebar;
