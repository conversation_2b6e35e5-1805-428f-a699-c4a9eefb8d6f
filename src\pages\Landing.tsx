import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LayoutDashboard, Package, DollarSign, BarChart2, Users } from 'lucide-react';
import { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from '@/components/ui/carousel';

const features = [
  {
    title: 'Affiliate Dashboard',
    description: 'Track your sales, earnings, and performance in real time with our intuitive dashboard.',
    icon: LayoutDashboard,
  },
  {
    title: 'Product Management',
    description: 'Easily browse, promote, and manage a wide range of products to maximize your earnings.',
    icon: Package,
  },
  {
    title: 'Instant Payouts',
    description: 'Get paid quickly and securely with our automated payout system.',
    icon: DollarSign,
  },
  {
    title: 'Analytics & Insights',
    description: 'Gain valuable insights into your affiliate campaigns and optimize for better results.',
    icon: BarChart2,
  },
  {
    title: 'Support & Community',
    description: 'Access dedicated support and join a thriving community of successful affiliates.',
    icon: Users,
  },
];

const testimonials = [
  {
    name: 'Sarah A.',
    role: 'Top Affiliate',
    quote: 'whary made it so easy to track my sales and boost my income!',
    avatar: 'https://randomuser.me/api/portraits/women/32.jpg'
  },
  {
    name: 'Mohamed B.',
    role: 'Product Promoter',
    quote: 'The instant payouts and analytics are a game changer for my business.',
    avatar: 'https://randomuser.me/api/portraits/men/22.jpg'
  },
  {
    name: 'Lina K.',
    role: 'Community Member',
    quote: 'I love the support and the community. Highly recommended!',
    avatar: 'https://randomuser.me/api/portraits/women/65.jpg'
  },
];

const faqs = [
  {
    question: "How do I earn commissions?",
    answer: "You earn commissions when someone makes a purchase using your unique affiliate link. The commission is calculated as a percentage of the product's price. You'll see the earnings in your dashboard after the purchase is confirmed."
  },
  {
    question: "When do I get paid?",
    answer: "Commissions are paid on a monthly basis, typically on the 15th of each month. You must reach a minimum threshold to receive a payout. Payments are made via your preferred method."
  },
  {
    question: "How do I create affiliate links?",
    answer: "To create an affiliate link, browse the Products section and click on the 'Copy Link' button for any product you want to promote. This generates a unique link that tracks back to your account."
  },
  {
    question: "What is the cookie duration?",
    answer: "The cookie duration is 30 days. This means that if someone clicks on your affiliate link and makes a purchase within 30 days, you will receive the commission, even if they didn't buy immediately after clicking."
  },
  {
    question: "Can I promote products on social media?",
    answer: "Yes, you can promote products on any platform including social media, your blog, email newsletters, or websites. Just make sure you're following the terms of service of each platform regarding promotional content."
  },
  {
    question: "How do I track my performance?",
    answer: "Your performance metrics are available in your dashboard. You can view detailed statistics including clicks, conversions, and earnings. Use the Earnings page for a comprehensive breakdown of your commission history."
  },
];

export default function Landing() {
  const navigate = useNavigate();
  const [faqOpen, setFaqOpen] = useState<number | null>(null);
  const [carouselIndex, setCarouselIndex] = useState(0);

  const screenshots = [
    { label: 'Affiliate Dashboard', img: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    { label: 'Analytics & Insights', img: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    { label: 'Product Management', img: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
    { label: 'Order Tracking', img: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=800&q=80' },
    { label: 'Instant Payouts', img: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80' },
    { label: 'Community & Support', img: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=800&q=80' },
  ];

  return (
    <div className="min-h-full w-full bg-background flex flex-col font-sans" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
      {/* Header */}
      <motion.header initial={{ y: -60, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ duration: 0.7, type: 'spring' }} className="flex items-center justify-between px-4 sm:px-10 py-4 sm:py-6 border-b border-border sticky top-0 z-10 bg-background w-full">
        <div className="flex items-center gap-3">
          <img src="/Logo.jpg" alt="whary logo" className="w-8 h-8 rounded-xl bg-primary object-cover" />
          <span className="text-lg sm:text-xl font-bold text-primary">whary</span>
        </div>
        <nav className="hidden lg:flex items-center gap-6 xl:gap-10 text-sm font-medium">
          <a href="#features" className="relative hover:text-foreground transition-colors text-muted-foreground">Features</a>
          <a href="#how" className="text-muted-foreground hover:text-foreground transition-colors">How it Works</a>
          <a href="#testimonials" className="hover:text-foreground transition-colors text-muted-foreground">Testimonials</a>
          <a href="#faq" className="hover:text-foreground transition-colors text-muted-foreground">FAQ</a>
        </nav>
        <div className="flex items-center gap-2">
          <Button onClick={() => navigate('/register')} className="rounded-full bg-primary text-primary-foreground px-4 sm:px-8 py-2 sm:py-3 text-sm font-semibold shadow-lg hover:bg-primary/80 transition-colors">Sign Up</Button>
          <Button onClick={() => navigate('/login')} variant="outline" className="rounded-full border-border text-primary px-4 sm:px-8 py-2 sm:py-3 text-sm font-semibold hover:bg-muted transition-colors">Login</Button>
        </div>
      </motion.header>

      {/* Hero Section */}
      <motion.section initial={{ opacity: 0, y: 40 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay: 0.2 }} className="flex flex-col items-center text-center px-4 sm:px-6 lg:px-10 py-12 sm:py-20 relative w-full bg-gradient-to-br from-background to-muted">
        <motion.div initial={{ scale: 0.95, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ delay: 0.4, duration: 0.7, type: 'spring' }} className="flex flex-col sm:flex-row items-center gap-3 mb-6 sm:mb-8">
          <div className="flex -space-x-2">
            <img src="https://randomuser.me/api/portraits/women/32.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <img src="https://randomuser.me/api/portraits/men/22.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <img src="https://randomuser.me/api/portraits/women/65.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <img src="https://randomuser.me/api/portraits/men/41.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <div className="w-8 h-8 rounded-full bg-primary border-2 border-card shadow-sm flex items-center justify-center">
              <span className="text-primary-foreground text-xs font-bold">+5K</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <div className="flex gap-0.5">
              {[...Array(5)].map((_, i) => (
                <svg key={i} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-star w-4 h-4 text-accent"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg>
              ))}
            </div>
            <span className="text-xs sm:text-sm text-muted-foreground ml-1">Trusted by 5,000+ affiliates</span>
          </div>
        </motion.div>
        <motion.h1 initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3, duration: 0.7 }} className="max-w-4xl text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-foreground mb-4 sm:mb-6">
          <span className="highlight font-bold text-primary bg-primary/10 px-2 rounded">Grow your affiliate earnings</span> with the all-in-one platform for modern marketers
        </motion.h1>
        <motion.p initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5, duration: 0.7 }} className="max-w-2xl text-base sm:text-lg text-muted-foreground mb-8 sm:mb-10 leading-relaxed px-4">
          Discover, promote, and track high-converting products. Get real-time analytics, instant payouts, and join a thriving community of top affiliates.
        </motion.p>
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6, duration: 0.7 }} className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-12 sm:mb-16 w-full sm:w-auto justify-center">
          <Button onClick={() => navigate('/register')} className="flex items-center justify-center gap-2 rounded-full bg-primary text-primary-foreground px-8 sm:px-10 py-3 sm:py-4 text-base font-semibold shadow-lg hover:bg-primary/80 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user-plus w-5 h-5"><path d="M15 14a4 4 0 1 0-8 0 4 4 0 0 0 8 0Z"/><path d="M6 18a6 6 0 0 1 12 0"/><path d="M19 8v6"/><path d="M22 11h-6"/></svg>
            Join as Affiliate
          </Button>
          <Button onClick={() => navigate('/login')} variant="outline" className="flex items-center justify-center gap-2 rounded-full bg-background border-2 border-border text-primary px-8 sm:px-10 py-3 sm:py-4 text-base font-semibold hover:bg-muted transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-log-in w-5 h-5"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/><polyline points="10 17 15 12 10 7"/><line x1="15" x2="3" y1="12" y2="12"/></svg>
            Demo Login
          </Button>
        </motion.div>
        {/* Hero media */}
        <motion.div initial={{ opacity: 0, scale: 0.98 }} animate={{ opacity: 1, scale: 1 }} transition={{ delay: 0.7, duration: 0.7 }} className="relative w-full flex justify-center">
          <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=1000&q=80" className="w-full max-w-5xl sm:rounded-2xl object-cover h-[250px] sm:h-[350px] lg:h-[400px] bg-cover rounded-xl mr-auto ml-auto shadow-2xl" alt="Affiliate dashboard preview" />
        </motion.div>
      </motion.section>

      {/* Features Section */}
      <motion.section
        id="features"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.7, delay: 0.1 }}
        className="py-20 lg:py-28 bg-gradient-to-br from-background via-muted/30 to-background w-full relative overflow-hidden"
      >
        {/* Background decorative elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16 lg:mb-20"
          >
            <Badge className="mb-4 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">
              ✨ Platform Features
            </Badge>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              Everything you need to
              <span className="block text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                succeed as an affiliate
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Our comprehensive platform provides all the tools and insights you need to maximize your affiliate earnings and grow your business.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {features.map((feature, idx) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.1 * idx, duration: 0.6 }}
                  className="group relative"
                >
                  <Card className="h-full bg-gradient-to-br from-card via-card to-muted/20 border border-border/50 hover:border-primary/30 rounded-3xl p-6 lg:p-8 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10 hover:-translate-y-2 backdrop-blur-sm">
                    {/* Hover glow effect */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <CardHeader className="flex flex-col items-center text-center p-0 mb-6">
                      <div className="relative mb-6">
                        <div className="w-20 h-20 lg:w-24 lg:h-24 flex items-center justify-center bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl border border-primary/20 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
                          <Icon className="w-10 h-10 lg:w-12 lg:h-12 text-primary group-hover:scale-110 transition-transform duration-300" />
                        </div>
                        {/* Floating badge */}
                        {idx === 0 && (
                          <Badge className="absolute -top-2 -right-2 bg-green-500 text-white border-0 shadow-lg animate-pulse">
                            New
                          </Badge>
                        )}
                        {idx === 2 && (
                          <Badge className="absolute -top-2 -right-2 bg-yellow-500 text-white border-0 shadow-lg animate-pulse">
                            Popular
                          </Badge>
                        )}
                      </div>
                      <CardTitle className="text-xl lg:text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <CardDescription className="text-muted-foreground text-center text-base lg:text-lg leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* Call to action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center mt-16 lg:mt-20"
          >
            <p className="text-lg text-muted-foreground mb-6">Ready to experience these features?</p>
            <Button
              onClick={() => navigate('/register')}
              className="rounded-full bg-primary text-primary-foreground px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl hover:shadow-primary/25 hover:bg-primary/90 transition-all duration-300 hover:scale-105"
            >
              Start Your Journey
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Button>
          </motion.div>
        </div>
      </motion.section>

      {/* Platform Screenshots Section */}
      <motion.section
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.7, delay: 0.1 }}
        className="py-20 lg:py-28 bg-gradient-to-br from-muted/20 via-background to-muted/30 w-full relative overflow-hidden"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-secondary/5 rounded-full blur-3xl"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16 lg:mb-20"
          >
            <Badge className="mb-4 bg-secondary/10 text-secondary-foreground border-secondary/20 hover:bg-secondary/20">
              🖥️ Platform Preview
            </Badge>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              See our platform
              <span className="block text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                in action
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Take a tour of our intuitive interface designed to help you track, manage, and optimize your affiliate campaigns with ease.
            </p>
          </motion.div>

          <div className="relative max-w-6xl mx-auto">
            {/* Enhanced Carousel Controls */}
            <button
              onClick={() => setCarouselIndex((prev) => (prev === 0 ? screenshots.length - 1 : prev - 1))}
              className="absolute left-4 lg:-left-16 top-1/2 -translate-y-1/2 z-20 bg-background/90 backdrop-blur-sm hover:bg-background border border-border/50 hover:border-primary/50 rounded-full p-3 lg:p-4 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 group"
              aria-label="Previous screenshot"
              type="button"
            >
              <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 lg:w-6 lg:h-6 group-hover:text-primary transition-colors">
                <path d="M15 18l-6-6 6-6" />
              </svg>
            </button>
            <button
              onClick={() => setCarouselIndex((prev) => (prev === screenshots.length - 1 ? 0 : prev + 1))}
              className="absolute right-4 lg:-right-16 top-1/2 -translate-y-1/2 z-20 bg-background/90 backdrop-blur-sm hover:bg-background border border-border/50 hover:border-primary/50 rounded-full p-3 lg:p-4 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 group"
              aria-label="Next screenshot"
              type="button"
            >
              <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5 lg:w-6 lg:h-6 group-hover:text-primary transition-colors">
                <path d="M9 18l6-6-6-6" />
              </svg>
            </button>

            {/* Enhanced Carousel */}
            <div className="flex flex-col items-center">
              <div className="w-full overflow-hidden rounded-3xl">
                <div className="flex transition-transform duration-700 ease-in-out" style={{ transform: `translateX(-${carouselIndex * 100}%)` }}>
                  {screenshots.map((shot, idx) => (
                    <motion.div
                      key={idx}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{
                        opacity: carouselIndex === idx ? 1 : 0.7,
                        scale: carouselIndex === idx ? 1 : 0.95
                      }}
                      transition={{ duration: 0.7, ease: "easeInOut" }}
                      className="relative flex-shrink-0 w-full h-[300px] sm:h-[400px] lg:h-[500px] group"
                    >
                      {/* Enhanced Device Mockup */}
                      <div className="absolute inset-4 lg:inset-8 rounded-2xl lg:rounded-3xl border-2 lg:border-4 border-primary/20 bg-gradient-to-br from-background to-muted shadow-2xl overflow-hidden group-hover:border-primary/40 transition-all duration-500">
                        {/* Screen bezel effect */}
                        <div className="absolute inset-0 bg-gradient-to-br from-background via-transparent to-muted/50 rounded-2xl lg:rounded-3xl"></div>

                        <img
                          src={shot.img}
                          alt={shot.label + ' Screenshot'}
                          className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-700 rounded-xl lg:rounded-2xl"
                        />

                        {/* Enhanced overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end justify-center pb-8 rounded-xl lg:rounded-2xl">
                          <div className="bg-background/95 backdrop-blur-sm px-6 py-3 rounded-2xl border border-primary/30 shadow-xl">
                            <span className="text-lg lg:text-xl font-semibold text-primary">{shot.label}</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Enhanced Carousel Dots */}
              <div className="flex justify-center gap-3 mt-8 lg:mt-12">
                {screenshots.map((_, idx) => (
                  <button
                    key={idx}
                    onClick={() => setCarouselIndex(idx)}
                    className={`w-3 h-3 lg:w-4 lg:h-4 rounded-full border-2 transition-all duration-300 hover:scale-125 ${
                      carouselIndex === idx
                        ? 'bg-primary border-primary shadow-lg shadow-primary/30'
                        : 'bg-background border-border hover:border-primary/50'
                    }`}
                    aria-label={`Go to screenshot ${idx + 1}`}
                    type="button"
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Enhanced CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center mt-16 lg:mt-20"
          >
            <div className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-3xl p-8 lg:p-12 border border-primary/20">
              <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-4">Ready to experience it yourself?</h3>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Join thousands of successful affiliates who are already using our platform to maximize their earnings.
              </p>
              <Button
                onClick={() => navigate('/register')}
                className="rounded-full bg-primary text-primary-foreground px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl hover:shadow-primary/25 hover:bg-primary/90 transition-all duration-300 hover:scale-105"
              >
                Get Started Free
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Button>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Testimonials Section */}
      <motion.section
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.7, delay: 0.1 }}
        className="py-20 lg:py-28 bg-gradient-to-br from-background via-primary/5 to-background w-full relative overflow-hidden"
        id="testimonials"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-20 right-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16 lg:mb-20"
          >
            <Badge className="mb-4 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">
              💬 Success Stories
            </Badge>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              What our affiliates
              <span className="block text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                are saying
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Join thousands of successful affiliates who have transformed their earnings with our platform.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {testimonials.map((testimonial, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 * idx, duration: 0.6 }}
                className="group"
              >
                <Card className="h-full bg-gradient-to-br from-card via-card to-muted/20 border border-border/50 hover:border-primary/30 rounded-3xl p-6 lg:p-8 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10 hover:-translate-y-2 backdrop-blur-sm">
                  {/* Quote icon */}
                  <div className="mb-6">
                    <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center">
                      <svg className="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                      </svg>
                    </div>
                  </div>

                  <CardContent className="p-0 mb-6">
                    <p className="text-muted-foreground text-base lg:text-lg leading-relaxed italic">
                      "{testimonial.quote}"
                    </p>
                  </CardContent>

                  <CardHeader className="p-0 flex flex-row items-center gap-4">
                    <div className="relative">
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-12 h-12 lg:w-14 lg:h-14 rounded-full border-2 border-primary/20 shadow-lg"
                      />
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background"></div>
                    </div>
                    <div>
                      <CardTitle className="text-lg font-semibold text-foreground">
                        {testimonial.name}
                      </CardTitle>
                      <CardDescription className="text-sm text-muted-foreground">
                        {testimonial.role}
                      </CardDescription>
                    </div>
                  </CardHeader>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Social proof */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center mt-16 lg:mt-20"
          >
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 lg:gap-12">
              <div className="text-center">
                <div className="text-3xl lg:text-4xl font-bold text-primary mb-2">5,000+</div>
                <div className="text-sm lg:text-base text-muted-foreground">Active Affiliates</div>
              </div>
              <div className="text-center">
                <div className="text-3xl lg:text-4xl font-bold text-primary mb-2">$2M+</div>
                <div className="text-sm lg:text-base text-muted-foreground">Commissions Paid</div>
              </div>
              <div className="text-center">
                <div className="text-3xl lg:text-4xl font-bold text-primary mb-2">98%</div>
                <div className="text-sm lg:text-base text-muted-foreground">Satisfaction Rate</div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* How It Works Section */}
      <motion.section
        id="how"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.7, delay: 0.1 }}
        className="py-20 lg:py-28 bg-gradient-to-br from-muted/20 via-background to-muted/30 w-full relative overflow-hidden"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-secondary/5 rounded-full blur-3xl"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16 lg:mb-20"
          >
            <Badge className="mb-4 bg-secondary/10 text-secondary-foreground border-secondary/20 hover:bg-secondary/20">
              🚀 Getting Started
            </Badge>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              How it works
              <span className="block text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                in 4 simple steps
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Start earning commissions in minutes with our streamlined onboarding process.
            </p>
          </motion.div>

          <div className="relative">
            {/* Enhanced connecting line for desktop */}
            <div className="hidden lg:block absolute left-0 right-0 top-24 h-1 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20 z-0 rounded-full" />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6 relative z-10">
              {[
                {
                  icon: '👤',
                  title: 'Create Account',
                  text: 'Sign up and create your affiliate account in under 2 minutes.',
                  color: 'from-blue-500/10 to-blue-600/10 border-blue-500/20'
                },
                {
                  icon: '🛒',
                  title: 'Choose Products',
                  text: 'Browse and select high-converting products from our curated marketplace.',
                  color: 'from-green-500/10 to-green-600/10 border-green-500/20'
                },
                {
                  icon: '🔗',
                  title: 'Share & Promote',
                  text: 'Share your unique affiliate links and start earning commissions on every sale.',
                  color: 'from-purple-500/10 to-purple-600/10 border-purple-500/20'
                },
                {
                  icon: '📈',
                  title: 'Track & Earn',
                  text: 'Monitor your performance in real-time and withdraw your earnings instantly.',
                  color: 'from-orange-500/10 to-orange-600/10 border-orange-500/20'
                },
              ].map((step, idx, arr) => (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.1 * idx, duration: 0.6 }}
                  className="flex flex-col items-center relative group"
                >
                  {/* Step number and icon */}
                  <div className="flex flex-col items-center mb-6 relative">
                    <div className={`w-20 h-20 lg:w-24 lg:h-24 rounded-3xl bg-gradient-to-br ${step.color} flex items-center justify-center text-3xl lg:text-4xl shadow-xl border group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 backdrop-blur-sm`}>
                      {step.icon}
                    </div>
                    <div className="absolute -bottom-3 bg-primary text-primary-foreground rounded-full px-4 py-1 text-sm font-bold shadow-lg border-4 border-background">
                      Step {idx + 1}
                    </div>
                  </div>

                  {/* Connecting dot for desktop */}
                  <div className="hidden lg:block w-6 h-6 rounded-full bg-primary border-4 border-background absolute top-[42px] left-1/2 -translate-x-1/2 z-20 shadow-lg" />

                  {/* Arrow between steps for desktop */}
                  {idx < arr.length - 1 && (
                    <div className="hidden lg:block absolute right-0 top-[48px] w-1/2 h-1 bg-gradient-to-r from-primary/40 to-transparent z-10" />
                  )}

                  {/* Step content card */}
                  <Card className={`bg-gradient-to-br ${step.color} border rounded-3xl p-6 text-center group-hover:shadow-2xl group-hover:-translate-y-2 transition-all duration-500 backdrop-blur-sm h-full`}>
                    <CardHeader className="p-0 mb-4">
                      <CardTitle className="text-xl lg:text-2xl font-bold text-foreground">
                        {step.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <CardDescription className="text-muted-foreground text-base lg:text-lg leading-relaxed">
                        {step.text}
                      </CardDescription>
                    </CardContent>
                  </Card>

                  {/* Mobile connecting line */}
                  {idx < arr.length - 1 && (
                    <div className="block lg:hidden w-1 h-12 bg-gradient-to-b from-primary/40 to-primary/20 rounded-full mx-auto my-6" />
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center mt-16 lg:mt-20"
          >
            <div className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-3xl p-8 lg:p-12 border border-primary/20">
              <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-4">Ready to get started?</h3>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Join our community of successful affiliates and start earning today. No setup fees, no hidden costs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  onClick={() => navigate('/register')}
                  className="rounded-full bg-primary text-primary-foreground px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl hover:shadow-primary/25 hover:bg-primary/90 transition-all duration-300 hover:scale-105"
                >
                  Start Earning Now
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Button>
                <Button
                  variant="outline"
                  className="rounded-full border-primary/30 text-primary hover:bg-primary/10 px-8 py-4 text-lg font-semibold"
                >
                  Learn More
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* FAQ Section */}
      <motion.section
        id="faq"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.7, delay: 0.1 }}
        className="py-20 lg:py-28 bg-gradient-to-br from-background via-muted/20 to-background w-full relative overflow-hidden"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"></div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16 lg:mb-20"
          >
            <Badge className="mb-4 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">
              ❓ FAQ
            </Badge>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              Frequently asked
              <span className="block text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                questions
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Everything you need to know about our affiliate platform and how to get started.
            </p>
          </motion.div>

          <div className="space-y-4 lg:space-y-6">
            {faqs.map((faq, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 * idx, duration: 0.6 }}
              >
                <Card className="bg-gradient-to-br from-card via-card to-muted/20 border border-border/50 hover:border-primary/30 rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <button
                    onClick={() => setFaqOpen(faqOpen === idx ? null : idx)}
                    className="w-full p-6 lg:p-8 text-left flex items-center justify-between hover:bg-muted/20 transition-colors duration-200"
                    type="button"
                  >
                    <h3 className="text-lg lg:text-xl font-semibold text-foreground pr-4">
                      {faq.question}
                    </h3>
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center transition-transform duration-300 ${faqOpen === idx ? 'rotate-180' : ''}`}>
                      <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                  <motion.div
                    initial={false}
                    animate={{
                      height: faqOpen === idx ? 'auto' : 0,
                      opacity: faqOpen === idx ? 1 : 0
                    }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 lg:px-8 pb-6 lg:pb-8">
                      <div className="h-px bg-border mb-4"></div>
                      <p className="text-muted-foreground text-base lg:text-lg leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Contact CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center mt-16 lg:mt-20"
          >
            <div className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-3xl p-8 lg:p-12 border border-primary/20">
              <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-4">Still have questions?</h3>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Our support team is here to help you succeed. Get in touch and we'll answer any questions you have.
              </p>
              <Button
                variant="outline"
                className="rounded-full border-primary/30 text-primary hover:bg-primary/10 px-8 py-4 text-lg font-semibold"
              >
                Contact Support
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </Button>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Enhanced Footer */}
      <footer className="bg-gradient-to-br from-muted/50 via-background to-muted/30 border-t border-border/50 w-full relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12 mb-12">
            {/* Brand section */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <img src="/Logo.jpg" alt="whary logo" className="w-10 h-10 rounded-xl bg-primary object-cover" />
                <span className="text-2xl font-bold text-primary">whary</span>
              </div>
              <p className="text-muted-foreground text-lg leading-relaxed mb-6 max-w-md">
                The all-in-one affiliate platform helping thousands of marketers grow their earnings with powerful tools and insights.
              </p>
              <div className="flex gap-4">
                <Button variant="outline" size="icon" className="rounded-full border-border/50 hover:border-primary/50 hover:bg-primary/10">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </Button>
                <Button variant="outline" size="icon" className="rounded-full border-border/50 hover:border-primary/50 hover:bg-primary/10">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </Button>
                <Button variant="outline" size="icon" className="rounded-full border-border/50 hover:border-primary/50 hover:bg-primary/10">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </Button>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold text-foreground mb-4">Quick Links</h4>
              <ul className="space-y-3">
                <li><a href="#features" className="text-muted-foreground hover:text-primary transition-colors">Features</a></li>
                <li><a href="#how" className="text-muted-foreground hover:text-primary transition-colors">How it Works</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-primary transition-colors">Testimonials</a></li>
                <li><a href="#faq" className="text-muted-foreground hover:text-primary transition-colors">FAQ</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="text-lg font-semibold text-foreground mb-4">Support</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Help Center</a></li>
                <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-muted-foreground hover:text-primary transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          {/* Bottom section */}
          <div className="border-t border-border/50 pt-8 flex flex-col sm:flex-row items-center justify-between gap-4">
            <p className="text-muted-foreground text-sm">
              &copy; {new Date().getFullYear()} whary. All rights reserved.
            </p>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <span>Made with ❤️ for affiliates</span>
              <Badge className="bg-green-500/10 text-green-600 border-green-500/20">
                🟢 All systems operational
              </Badge>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
} 