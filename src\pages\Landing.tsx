import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LayoutDashboard, Package, DollarSign, BarChart2, Users } from 'lucide-react';
import { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from '@/components/ui/carousel';

const features = [
  {
    title: 'Affiliate Dashboard',
    description: 'Track your sales, earnings, and performance in real time with our intuitive dashboard.',
    icon: LayoutDashboard,
  },
  {
    title: 'Product Management',
    description: 'Easily browse, promote, and manage a wide range of products to maximize your earnings.',
    icon: Package,
  },
  {
    title: 'Instant Payouts',
    description: 'Get paid quickly and securely with our automated payout system.',
    icon: DollarSign,
  },
  {
    title: 'Analytics & Insights',
    description: 'Gain valuable insights into your affiliate campaigns and optimize for better results.',
    icon: BarChart2,
  },
  {
    title: 'Support & Community',
    description: 'Access dedicated support and join a thriving community of successful affiliates.',
    icon: Users,
  },
];

const testimonials = [
  {
    name: 'Sarah A.',
    role: 'Top Affiliate',
    quote: 'whary made it so easy to track my sales and boost my income!',
    avatar: 'https://randomuser.me/api/portraits/women/32.jpg'
  },
  {
    name: 'Mohamed B.',
    role: 'Product Promoter',
    quote: 'The instant payouts and analytics are a game changer for my business.',
    avatar: 'https://randomuser.me/api/portraits/men/22.jpg'
  },
  {
    name: 'Lina K.',
    role: 'Community Member',
    quote: 'I love the support and the community. Highly recommended!',
    avatar: 'https://randomuser.me/api/portraits/women/65.jpg'
  },
];

const faqs = [
  {
    question: "How do I earn commissions?",
    answer: "You earn commissions when someone makes a purchase using your unique affiliate link. The commission is calculated as a percentage of the product's price. You'll see the earnings in your dashboard after the purchase is confirmed."
  },
  {
    question: "When do I get paid?",
    answer: "Commissions are paid on a monthly basis, typically on the 15th of each month. You must reach a minimum threshold to receive a payout. Payments are made via your preferred method."
  },
  {
    question: "How do I create affiliate links?",
    answer: "To create an affiliate link, browse the Products section and click on the 'Copy Link' button for any product you want to promote. This generates a unique link that tracks back to your account."
  },
  {
    question: "What is the cookie duration?",
    answer: "The cookie duration is 30 days. This means that if someone clicks on your affiliate link and makes a purchase within 30 days, you will receive the commission, even if they didn't buy immediately after clicking."
  },
  {
    question: "Can I promote products on social media?",
    answer: "Yes, you can promote products on any platform including social media, your blog, email newsletters, or websites. Just make sure you're following the terms of service of each platform regarding promotional content."
  },
  {
    question: "How do I track my performance?",
    answer: "Your performance metrics are available in your dashboard. You can view detailed statistics including clicks, conversions, and earnings. Use the Earnings page for a comprehensive breakdown of your commission history."
  },
];

export default function Landing() {
  const navigate = useNavigate();
  const [faqOpen, setFaqOpen] = useState<number | null>(null);
  const [carouselIndex, setCarouselIndex] = useState(0);

  const screenshots = [
    { label: 'Affiliate Dashboard', img: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    { label: 'Analytics & Insights', img: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    { label: 'Product Management', img: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
    { label: 'Order Tracking', img: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=800&q=80' },
    { label: 'Instant Payouts', img: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80' },
    { label: 'Community & Support', img: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=800&q=80' },
  ];

  return (
    <div className="min-h-full w-full bg-background flex flex-col font-sans" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
      {/* Header */}
      <motion.header initial={{ y: -60, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ duration: 0.7, type: 'spring' }} className="flex items-center justify-between px-4 sm:px-10 py-4 sm:py-6 border-b border-border sticky top-0 z-10 bg-background w-full">
        <div className="flex items-center gap-3">
          <img src="/Logo.jpg" alt="whary logo" className="w-8 h-8 rounded-xl bg-primary object-cover" />
          <span className="text-lg sm:text-xl font-bold text-primary">whary</span>
        </div>
        <nav className="hidden lg:flex items-center gap-6 xl:gap-10 text-sm font-medium">
          <a href="#features" className="relative hover:text-foreground transition-colors text-muted-foreground">Features</a>
          <a href="#how" className="text-muted-foreground hover:text-foreground transition-colors">How it Works</a>
          <a href="#testimonials" className="hover:text-foreground transition-colors text-muted-foreground">Testimonials</a>
          <a href="#faq" className="hover:text-foreground transition-colors text-muted-foreground">FAQ</a>
        </nav>
        <div className="flex items-center gap-2">
          <Button onClick={() => navigate('/register')} className="rounded-full bg-primary text-primary-foreground px-4 sm:px-8 py-2 sm:py-3 text-sm font-semibold shadow-lg hover:bg-primary/80 transition-colors">Sign Up</Button>
          <Button onClick={() => navigate('/login')} variant="outline" className="rounded-full border-border text-primary px-4 sm:px-8 py-2 sm:py-3 text-sm font-semibold hover:bg-muted transition-colors">Login</Button>
        </div>
      </motion.header>

      {/* Hero Section */}
      <motion.section initial={{ opacity: 0, y: 40 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay: 0.2 }} className="flex flex-col items-center text-center px-4 sm:px-6 lg:px-10 py-12 sm:py-20 relative w-full bg-gradient-to-br from-background to-muted">
        <motion.div initial={{ scale: 0.95, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ delay: 0.4, duration: 0.7, type: 'spring' }} className="flex flex-col sm:flex-row items-center gap-3 mb-6 sm:mb-8">
          <div className="flex -space-x-2">
            <img src="https://randomuser.me/api/portraits/women/32.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <img src="https://randomuser.me/api/portraits/men/22.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <img src="https://randomuser.me/api/portraits/women/65.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <img src="https://randomuser.me/api/portraits/men/41.jpg" className="w-8 h-8 rounded-full border-2 border-card shadow-sm" />
            <div className="w-8 h-8 rounded-full bg-primary border-2 border-card shadow-sm flex items-center justify-center">
              <span className="text-primary-foreground text-xs font-bold">+5K</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <div className="flex gap-0.5">
              {[...Array(5)].map((_, i) => (
                <svg key={i} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-star w-4 h-4 text-accent"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg>
              ))}
            </div>
            <span className="text-xs sm:text-sm text-muted-foreground ml-1">Trusted by 5,000+ affiliates</span>
          </div>
        </motion.div>
        <motion.h1 initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3, duration: 0.7 }} className="max-w-4xl text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-foreground mb-4 sm:mb-6">
          <span className="highlight font-bold text-primary bg-primary/10 px-2 rounded">Grow your affiliate earnings</span> with the all-in-one platform for modern marketers
        </motion.h1>
        <motion.p initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5, duration: 0.7 }} className="max-w-2xl text-base sm:text-lg text-muted-foreground mb-8 sm:mb-10 leading-relaxed px-4">
          Discover, promote, and track high-converting products. Get real-time analytics, instant payouts, and join a thriving community of top affiliates.
        </motion.p>
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6, duration: 0.7 }} className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-12 sm:mb-16 w-full sm:w-auto justify-center">
          <Button onClick={() => navigate('/register')} className="flex items-center justify-center gap-2 rounded-full bg-primary text-primary-foreground px-8 sm:px-10 py-3 sm:py-4 text-base font-semibold shadow-lg hover:bg-primary/80 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user-plus w-5 h-5"><path d="M15 14a4 4 0 1 0-8 0 4 4 0 0 0 8 0Z"/><path d="M6 18a6 6 0 0 1 12 0"/><path d="M19 8v6"/><path d="M22 11h-6"/></svg>
            Join as Affiliate
          </Button>
          <Button onClick={() => navigate('/login')} variant="outline" className="flex items-center justify-center gap-2 rounded-full bg-background border-2 border-border text-primary px-8 sm:px-10 py-3 sm:py-4 text-base font-semibold hover:bg-muted transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-log-in w-5 h-5"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/><polyline points="10 17 15 12 10 7"/><line x1="15" x2="3" y1="12" y2="12"/></svg>
            Demo Login
          </Button>
        </motion.div>
        {/* Hero media */}
        <motion.div initial={{ opacity: 0, scale: 0.98 }} animate={{ opacity: 1, scale: 1 }} transition={{ delay: 0.7, duration: 0.7 }} className="relative w-full flex justify-center">
          <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=1000&q=80" className="w-full max-w-5xl sm:rounded-2xl object-cover h-[250px] sm:h-[350px] lg:h-[400px] bg-cover rounded-xl mr-auto ml-auto shadow-2xl" alt="Affiliate dashboard preview" />
        </motion.div>
      </motion.section>

      {/* Features Section */}
      <motion.section id="features" initial={{ opacity: 0, y: 40 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} transition={{ duration: 0.7, delay: 0.1 }} className="py-16 bg-background w-full">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8 text-primary">Platform Features</h2>
          <div className="flex gap-6 overflow-x-auto scrollbar-hide sm:grid sm:grid-cols-2 md:grid-cols-3 sm:gap-8">
            {features.map((feature, idx) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.1 * idx, duration: 0.6 }}
                  className="min-w-[260px] sm:min-w-0 group relative"
                >
                  <Card className="stats-card hover:shadow-2xl transition group bg-gradient-to-br from-primary/5 to-muted border-0 rounded-2xl p-1">
                    <CardHeader className="flex flex-col items-center">
                      <div className="mb-3 w-16 h-16 flex items-center justify-center bg-primary-foreground rounded-full border-2 border-primary shadow-lg group-hover:scale-105 transition-transform duration-300">
                        <Icon className="w-8 h-8 text-primary animate-bounce group-hover:animate-pulse" />
                      </div>
                      <CardTitle className="text-primary text-center text-xl flex items-center gap-2">
                        {feature.title}
                        {idx === 0 && <Badge className="ml-2 bg-green-500 text-white">New</Badge>}
                        {idx === 2 && <Badge className="ml-2 bg-yellow-500 text-white">Popular</Badge>}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-muted-foreground text-center text-base">{feature.description}</CardDescription>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </motion.section>

      {/* Platform Screenshots Section */}
      <motion.section initial={{ opacity: 0, y: 40 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} transition={{ duration: 0.7, delay: 0.1 }} className="py-20 bg-gradient-to-br from-background to-muted w-full relative overflow-x-hidden">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8 text-primary">See the Platform</h2>
          <div className="relative">
            {/* Carousel Controls */}
            <button
              onClick={() => setCarouselIndex((prev) => (prev === 0 ? screenshots.length - 1 : prev - 1))}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 hover:bg-background border border-border rounded-full p-2 shadow transition hidden sm:block"
              aria-label="Previous screenshot"
              type="button"
            >
              <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6"><path d="M15 18l-6-6 6-6" /></svg>
            </button>
            <button
              onClick={() => setCarouselIndex((prev) => (prev === screenshots.length - 1 ? 0 : prev + 1))}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 hover:bg-background border border-border rounded-full p-2 shadow transition hidden sm:block"
              aria-label="Next screenshot"
              type="button"
            >
              <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6"><path d="M9 18l6-6-6-6" /></svg>
            </button>
            {/* Carousel */}
            <div className="flex flex-col items-center">
              <div className="w-full overflow-x-auto scrollbar-hide">
                <div className="flex gap-8 transition-transform duration-500" style={{ transform: `translateX(-${carouselIndex * 100}%)` }}>
                  {screenshots.map((shot, idx) => (
                    <motion.div
                      key={idx}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: carouselIndex === idx ? 1 : 0.5, scale: carouselIndex === idx ? 1 : 0.95 }}
                      transition={{ duration: 0.5 }}
                      className={`relative flex-shrink-0 w-[320px] sm:w-[400px] md:w-[480px] h-[300px] sm:h-[340px] md:h-[380px] mx-auto group ${carouselIndex === idx ? 'z-10' : 'z-0'}`}
                    >
                      {/* Device Mockup */}
                      <div className="absolute inset-0 rounded-2xl border-4 border-primary bg-background shadow-2xl overflow-hidden flex items-center justify-center">
                        <img src={shot.img} alt={shot.label + ' Screenshot'} className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-500" />
                        <div className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <span className="text-lg font-semibold text-primary drop-shadow-lg bg-background/80 px-4 py-2 rounded-xl border border-primary shadow">{shot.label}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
              {/* Carousel Dots */}
              <div className="flex justify-center gap-2 mt-6">
                {screenshots.map((_, idx) => (
                  <button
                    key={idx}
                    onClick={() => setCarouselIndex(idx)}
                    className={`w-3 h-3 rounded-full border border-primary transition ${carouselIndex === idx ? 'bg-primary' : 'bg-background'}`}
                    aria-label={`Go to screenshot ${idx + 1}`}
                    type="button"
                  />
                ))}
              </div>
            </div>
          </div>
          {/* CTA below carousel */}
          <div className="mt-10 flex flex-col items-center">
            <span className="text-lg text-muted-foreground mb-2">Ready to experience it yourself?</span>
            <Button onClick={() => navigate('/register')} className="rounded-full bg-primary text-primary-foreground px-8 py-3 text-base font-semibold shadow-lg hover:bg-primary/80 transition-colors">Get Started Free</Button>
          </div>
        </div>
      </motion.section>

      {/* Testimonials Section */}
      {/* FAQ Section */}

      {/* How It Works Section */}
      <motion.section id="how" initial={{ opacity: 0, y: 40 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} transition={{ duration: 0.7, delay: 0.1 }} className="py-20 bg-gradient-to-r from-muted to-background w-full">
        <div className="max-w-5xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-10 text-primary">How It Works</h2>
          <div className="relative flex flex-col sm:block">
            {/* Horizontal line for desktop */}
            <div className="hidden sm:block absolute left-0 right-0 top-16 h-1 bg-primary/20 z-0" style={{}} />
            <div className="flex flex-col sm:flex-row items-center justify-between w-full relative z-10">
              {[
                { icon: '👤', text: 'Sign up and create your affiliate account.' },
                { icon: '🛒', text: 'Browse and select products to promote from our marketplace.' },
                { icon: '🔗', text: 'Share your unique affiliate links and start earning commissions on every sale.' },
                { icon: '📈', text: 'Track your performance and withdraw your earnings anytime.' },
              ].map((step, idx, arr) => (
                <div key={idx} className="flex flex-col items-center flex-1 min-w-[180px] max-w-[220px] mx-auto relative">
                  {/* Step icon and number above the line */}
                  <div className="flex flex-col items-center mb-2">
                    <div className="w-14 h-14 rounded-full bg-primary flex items-center justify-center text-2xl text-primary-foreground shadow-lg border-4 border-background z-10">
                      {step.icon}
                    </div>
                    <span className="mt-2 bg-background border border-primary text-primary rounded-full px-3 py-1 text-xs font-bold shadow z-10">Step {idx + 1}</span>
                  </div>
                  {/* Dot on the line for desktop */}
                  <div className="hidden sm:block w-4 h-4 rounded-full bg-primary border-4 border-background absolute top-[56px] left-1/2 -translate-x-1/2 z-20" />
                  {/* Arrow/dot between steps for desktop */}
                  {idx < arr.length - 1 && (
                    <div className="hidden sm:block absolute right-0 top-[62px] w-1/2 h-1 bg-primary/30 z-10" />
                  )}
                  {/* Step card/description below the line */}
                  <div className="bg-background border border-primary/30 rounded-xl shadow-lg p-4 text-center text-base font-medium mt-8 sm:mt-12">
                    {step.text}
                  </div>
                  {/* Mobile connecting line */}
                  {idx < arr.length - 1 && (
                    <div className="block sm:hidden w-1 h-8 bg-primary/20 rounded-full mx-auto my-2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </motion.section>

      {/* FAQ Section */}
      {/* Footer */}
      <footer className="py-8 text-center text-muted-foreground text-sm bg-background w-full border-t border-border mt-auto">
        &copy; {new Date().getFullYear()} whary. All rights reserved.
      </footer>
    </div>
  );
} 