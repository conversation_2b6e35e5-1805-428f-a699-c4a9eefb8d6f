import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, ShieldCheck, Phone, CreditCard, CheckCircle2 } from 'lucide-react';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

const ILLUSTRATION = '/public/images/login-illustration.svg';
const BG_ILLUSTRATION = '/public/images/register-illustration.svg';

const RIP_PREFIX = '00799999';
const RIP_DIGITS = 12;

const CompleteProfile = () => {
  const { profile, updateProfile, profileLoading, refreshProfile } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    phone: '',
    rip: '',
    confirmRip: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [ripError, setRipError] = useState<string | null>(null);
  const [phoneError, setPhoneError] = useState<string | null>(null);

  useEffect(() => {
    if (profile) {
      setFormData(prev => ({
        ...prev,
        phone: profile.phone || '',
        rip: (profile.rip && profile.rip.startsWith(RIP_PREFIX)) ? profile.rip.slice(RIP_PREFIX.length) : '',
        confirmRip: (profile.rip && profile.rip.startsWith(RIP_PREFIX)) ? profile.rip.slice(RIP_PREFIX.length) : '',
      }));
    }
  }, [profile]);

  // If profile is already complete, redirect immediately
  useEffect(() => {
    if (
      profile &&
      profile.phone && profile.phone !== '' &&
      profile.rip && profile.rip !== ''
    ) {
      navigate('/', { replace: true });
    }
  }, [profile, navigate]);

  const validatePhone = (phone: string) => {
    const cleanPhone = phone.replace(/\s+/g, '');
    if (!/^0[567]\d{8}$/.test(cleanPhone)) {
      return 'Phone must be 10 digits and start with 05, 06, or 07.';
    }
    return '';
  };

  const validateRip = (rip: string) => {
    if (!/^\d{12}$/.test(rip)) {
      return 'RIP must be exactly 12 digits.';
    }
    return '';
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'rip' || name === 'confirmRip') {
      const digits = value.replace(/\D/g, '').slice(0, RIP_DIGITS);
      setFormData(prev => ({ ...prev, [name]: digits }));
      if (name === 'rip' || name === 'confirmRip') setRipError(null);
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      if (name === 'phone') setPhoneError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setRipError(null);
    setPhoneError(null);
    const phoneErr = validatePhone(formData.phone);
    const ripErr = validateRip(formData.rip);
    if (phoneErr) {
      setPhoneError(phoneErr);
      return;
    }
    if (ripErr) {
      setRipError(ripErr);
      return;
    }
    if (formData.rip !== formData.confirmRip) {
      setRipError('RIP numbers do not match.');
      return;
    }
    setIsSubmitting(true);
    try {
      const { error } = await updateProfile({
        phone: formData.phone,
        rip: RIP_PREFIX + formData.rip,
      });
      if (error) {
        toast.error(error.message || 'Failed to update profile');
      } else {
        await refreshProfile();
        navigate('/', { replace: true });
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (profileLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-emerald-50 animate-fade-in">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div
      className="min-h-screen w-full flex items-center justify-center animate-fade-in relative overflow-hidden"
      style={{
        background:
          'linear-gradient(90deg, #e0f2fe 0%, #f0fdf4 100%)',
      }}
    >
      <style>{`
        @media (max-width: 640px) {
          .complete-profile-bg {
            background: linear-gradient(180deg, #e0f2fe 0%, #f0fdf4 100%) !important;
          }
        }
        @media (min-width: 641px) {
          .complete-profile-bg {
            background: linear-gradient(90deg, #e0f2fe 0%, #f0fdf4 100%) !important;
          }
        }
      `}</style>
      <div className="complete-profile-bg fixed inset-0 -z-10" />
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 40, scale: 0.98 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -40, scale: 0.98 }}
          transition={{ duration: 0.5, type: 'spring', bounce: 0.18 }}
          className="w-full max-w-2xl z-10"
        >
          <Card className="border-0 shadow-2xl rounded-2xl bg-white/90 backdrop-blur-md overflow-hidden">
            <div className="flex flex-col md:flex-row">
              <div className="flex flex-col items-center justify-center md:w-1/2 p-6 md:border-r border-border bg-white/80">
                <img
                  src={ILLUSTRATION}
                  alt="Complete Profile"
                  className="w-24 h-24 mb-4 drop-shadow-md animate-fade-in"
                  style={{ background: 'white', borderRadius: '50%' }}
                  onError={e => (e.currentTarget.style.display = 'none')}
                />
                <div className="flex items-center gap-2 mb-2">
                  <ShieldCheck className="text-primary" size={22} />
                  <span className="font-semibold text-lg text-primary">Step 1 of 1</span>
                </div>
                <CardTitle className="text-xl font-bold text-center mb-1">Complete Your Profile</CardTitle>
                <CardDescription className="text-center text-base text-muted-foreground">
                  <span className="block font-medium text-emerald-700 mb-1">You're almost there!</span>
                  For your security and to receive payments, please provide your phone number and RIP.
                </CardDescription>
              </div>
              <div className="flex-1 p-6 flex flex-col justify-center">
                <CardContent className="p-0">
                  <form onSubmit={handleSubmit} className="space-y-6 mt-2" autoComplete="off">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="flex items-center gap-1">
                        <Phone className="w-4 h-4 text-blue-500" /> Phone Number
                      </Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="05XXXXXXXX"
                        maxLength={10}
                        inputMode="numeric"
                        required
                        className={phoneError ? 'border-red-500 focus:border-red-500' : ''}
                        autoFocus
                        aria-invalid={!!phoneError}
                        aria-describedby="phone-help"
                      />
                      <p id="phone-help" className={`text-xs mt-1 ${phoneError ? 'text-red-500' : 'text-muted-foreground'}`}
                      >Enter a valid Algerian phone number (10 digits, starts with 05, 06, or 07).</p>
                      {phoneError && <p className="text-xs text-red-500 mt-1">{phoneError}</p>}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rip" className="flex items-center gap-1">
                        <CreditCard className="w-4 h-4 text-emerald-500" /> RIP
                      </Label>
                      <div className="flex items-center gap-0">
                        <span
                          className="inline-flex items-center justify-center h-10 px-3 border border-input border-r-0 rounded-l-md bg-muted text-sm font-mono select-none"
                          style={{ minWidth: '92px' }}
                        >
                          {RIP_PREFIX}
                        </span>
                        <Input
                          id="rip"
                          name="rip"
                          value={formData.rip}
                          onChange={handleInputChange}
                          placeholder={"12 digits"}
                          maxLength={12}
                          inputMode="numeric"
                          required
                          className={
                            (ripError ? 'border-red-500 focus:border-red-500 ' : '') +
                            'rounded-l-none h-10'
                          }
                          aria-invalid={!!ripError}
                          aria-describedby="rip-help"
                          style={{ borderLeft: 'none' }}
                        />
                      </div>
                      <p id="rip-help" className="text-xs mt-1 text-muted-foreground">
                        Your RIP is required to receive affiliate payments (12 digits after the prefix).
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmRip">Confirm RIP</Label>
                      <div className="flex items-center gap-0">
                        <span
                          className="inline-flex items-center justify-center h-10 px-3 border border-input border-r-0 rounded-l-md bg-muted text-sm font-mono select-none"
                          style={{ minWidth: '92px' }}
                        >
                          {RIP_PREFIX}
                        </span>
                        <Input
                          id="confirmRip"
                          name="confirmRip"
                          value={formData.confirmRip}
                          onChange={handleInputChange}
                          placeholder={"Re-enter RIP"}
                          maxLength={12}
                          inputMode="numeric"
                          required
                          className={
                            (ripError ? 'border-red-500 focus:border-red-500 ' : '') +
                            'rounded-l-none h-10'
                          }
                          aria-invalid={!!ripError}
                          style={{ borderLeft: 'none' }}
                        />
                      </div>
                      {ripError && <p className="text-xs text-red-500 mt-1">{ripError}</p>}
                    </div>
                    <Button type="submit" className="w-full mt-2" disabled={isSubmitting} size="lg">
                      {isSubmitting ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</> : 'Save and Continue'}
                    </Button>
                  </form>
                </CardContent>
              </div>
            </div>
          </Card>
        </motion.div>
      </AnimatePresence>
      <img
        src={BG_ILLUSTRATION}
        alt=""
        aria-hidden
        className="fixed bottom-0 right-0 w-64 opacity-10 pointer-events-none select-none z-0 animate-fade-in"
        onError={e => (e.currentTarget.style.display = 'none')}
      />
      <div className="fixed inset-0 bg-white/40 backdrop-blur-sm z-0 animate-fade-in" />
    </div>
  );
};

export default CompleteProfile; 