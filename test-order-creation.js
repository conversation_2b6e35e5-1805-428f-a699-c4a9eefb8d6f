// Simple test script to verify order creation functionality
// Run this in the browser console after logging in to test the fix

async function testOrderCreation() {
  console.log('Testing order creation with fixed affiliate worker foreign key...');
  
  try {
    // Import the necessary functions (this would work in the browser console)
    const { createOrder } = await import('./src/lib/api-extended.ts');
    const { createCustomer } = await import('./src/lib/api.ts');
    
    // Test data
    const testCustomer = {
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '0123456789',
      location: 'Algiers, Test Commune',
      address: 'Test Address 123'
    };
    
    console.log('Creating test customer...');
    const customer = await createCustomer(testCustomer);
    console.log('Customer created:', customer);
    
    // Get current user (should be logged in)
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    console.log('Current user:', user.id);
    
    const testOrder = {
      customer_id: customer.id,
      affiliate_worker_id: user.id, // This should now work with the fixed foreign key
      payment_method: 'Cash on Delivery',
      shipping_cost: 500,
      notes: 'Test order for foreign key fix',
      items: [{
        product_id: 'test-product-id', // Replace with actual product ID
        quantity: 1,
        price: 1000,
        affiliate_earnings: 100
      }]
    };
    
    console.log('Creating test order...');
    const order = await createOrder(testOrder);
    console.log('Order created successfully:', order);
    
    console.log('✅ Test passed! Order creation works with fixed foreign key.');
    return order;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Instructions for manual testing:
console.log(`
To test the order creation fix:

1. Make sure you've run the database migration script (database-fix-affiliate-worker-fk.sql)
2. Log in to the application
3. Open the browser console
4. Run: testOrderCreation()

Or test through the UI:
1. Go to a product detail page
2. Click "Add Buyer" 
3. Fill out the form with valid data
4. Submit the order
5. Check that no foreign key errors occur
`);

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testOrderCreation = testOrderCreation;
}
