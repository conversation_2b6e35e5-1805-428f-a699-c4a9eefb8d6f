import { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Heart, Copy, ShoppingCart, ArrowLeft, Check, Star, Loader2 } from "lucide-react";
import { getEnhancedProductById, getEnhancedProducts } from '@/lib/api';
import CopyLinkDialog from '@/components/product/CopyLinkDialog';
import AddBuyerDialog from '@/components/product/AddBuyerDialog';
import { formatCurrencyDa } from '@/lib/utils';
import { useFavorites } from '@/contexts/FavoritesContext';

const ProductDetail = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { isFavorited, addToFavorites, removeFromFavorites } = useFavorites();
  const [product, setProduct] = useState<any>(null);
  const [similarProducts, setSimilarProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);

  // State for dialogs
  const [copyLinkDialogOpen, setCopyLinkDialogOpen] = useState(false);
  const [addBuyerDialogOpen, setAddBuyerDialogOpen] = useState(false);

  // Check if this product is favorited
  const isFavorite = product ? isFavorited(product.id.toString()) : false;

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true);

      try {
        // Fetch product details from API
        const productData = await getEnhancedProductById(productId as string);
        setProduct(productData);

        // Fetch similar products
        const allProducts = await getEnhancedProducts();
        const similarProducts = allProducts
          .filter(p => p.category === productData.category && p.id !== productData.id)
          .slice(0, 4);
        setSimilarProducts(similarProducts);
      } catch (error) {
        console.error('Error fetching product:', error);
        toast.error("Product not found");
        navigate('/products');
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId, navigate]);

  if (loading || !product) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[70vh]">
          <div className="w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
        </div>
      </Layout>
    );
  }

  const commissionAmount = (product.price * product.commission) / 100;

  // Get product name (handle API data format)
  const productName = product.title || product.name;

  // Get product images
  const productImages = product.images || [product.image];

  // Fallback image in case the main one fails
  const fallbackImage = `https://picsum.photos/seed/${product.id}/800/600`;

  // Product options (would come from API in a real app)
  const availableColors = product.colors || [];
  const availableSizes = product.sizes || [];

  const toggleFavorite = async () => {
    if (!product) return;

    if (isFavorite) {
      await removeFromFavorites(product.id.toString());
    } else {
      await addToFavorites(product);
    }
  };

  const copyAffiliateLink = () => {
    // Open the copy link dialog
    setCopyLinkDialogOpen(true);
  };

  const addBuyer = () => {
    // Open the add buyer dialog
    setAddBuyerDialogOpen(true);
  };

  return (
    <Layout>
      <div className="mb-4">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4"
          onClick={() => navigate('/products')}
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Products
        </Button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Image Gallery */}
          <div className="space-y-4">
            <div className="relative rounded-lg overflow-hidden border border-gray-100 aspect-square">
              {/* Shimmer loading effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer bg-[length:200%_100%]"></div>

              {/* Product image container */}
              <div className="absolute inset-0 flex items-center justify-center p-4">
                <img
                  src={productImages[selectedImage]}
                  alt={productName}
                  className="max-w-full max-h-full object-contain transition-all duration-300 rounded-md"
                  onError={(e) => {
                    e.currentTarget.src = fallbackImage;
                  }}
                />
              </div>

              {/* Commission badge */}
              <div className="absolute top-3 right-3 z-10">
                <Badge variant="default" className="bg-black/90 hover:bg-black text-white font-bold shadow-sm">
                  {Math.round(product.commission)}% Commission
                </Badge>
              </div>
            </div>

            {/* Image thumbnails */}
            {productImages.length > 1 && (
              <div className="flex gap-2 overflow-x-auto pb-2">
                {productImages.map((image: string, index: number) => (
                  <button
                    key={index}
                    className={`relative rounded-md overflow-hidden border-2 h-16 w-16 sm:h-20 sm:w-20 flex-shrink-0 ${
                      selectedImage === index ? "border-black" : "border-transparent hover:border-gray-300"
                    }`}
                    onClick={() => setSelectedImage(index)}
                    aria-label={`View image ${index + 1}`}
                  >
                    <div className="absolute inset-0 flex items-center justify-center p-1">
                      <img
                        src={image}
                        alt={`${productName} - Thumbnail ${index + 1}`}
                        className="max-w-full max-h-full object-contain rounded-sm"
                        onError={(e) => {
                          e.currentTarget.src = fallbackImage;
                        }}
                      />
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="flex flex-col">
            <div className="flex justify-between items-start">
              <h1 className="text-2xl sm:text-3xl font-bold">{productName}</h1>

              <Button
                variant={isFavorite ? "default" : "outline"}
                size="icon"
                onClick={toggleFavorite}
                className={isFavorite ? "bg-red-500 hover:bg-red-600 border-red-500" : ""}
              >
                <Heart size={18} className={isFavorite ? "text-white fill-white" : ""} />
              </Button>
            </div>

            <div className="flex items-center gap-2 mt-2 mb-4">
              <div className="flex items-center">
                <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                <span className="ml-1 text-sm font-medium">{product.rating.rate}</span>
              </div>
              <span className="text-sm text-muted-foreground">({product.rating.count} reviews)</span>

              <Badge
                variant="secondary"
                className="text-xs px-2.5 py-1 ml-2"
              >
                {product.category}
              </Badge>
            </div>

            <div className="flex items-center mb-6">
              <div>
                <p className="text-xl font-bold">{formatCurrencyDa(product.price)}</p>
                <p className="text-sm text-emerald-600 font-semibold">
                  Earn: {formatCurrencyDa(commissionAmount)}
                </p>
              </div>
            </div>

            {/* Product Description */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Description</h3>
              <p className="text-muted-foreground">{product.description}</p>
            </div>

            {/* Product Information */}
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Product Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Category</p>
                    <p className="text-sm text-muted-foreground">{product.category}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Rating</p>
                    <div className="flex items-center">
                      <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                      <span className="ml-1 text-sm text-muted-foreground">
                        {product.rating.rate} ({product.rating.count} reviews)
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 mt-auto">
              <Button
                variant="outline"
                className="flex-1 gap-2"
                onClick={copyAffiliateLink}
              >
                <Copy size={16} />
                Copy Affiliate Link
              </Button>

              <Button
                variant="default"
                className="flex-1 gap-2"
                onClick={addBuyer}
              >
                <ShoppingCart size={16} />
                Add Buyer
              </Button>
            </div>
          </div>
        </div>

        {/* Similar Products Section */}
        {similarProducts.length > 0 && (
          <div className="mt-10">
            <h2 className="text-xl font-bold mb-4">Similar Products</h2>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {similarProducts.map((similarProduct) => (
                <Card
                  key={similarProduct.id}
                  className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => navigate(`/products/${similarProduct.id}`)}
                >
                  <div className="aspect-square relative overflow-hidden border border-gray-100">
                    <div className="absolute inset-0 flex items-center justify-center p-2">
                      <img
                        src={similarProduct.image}
                        alt={similarProduct.title}
                        className="max-w-full max-h-full object-contain rounded-md"
                        onError={(e) => {
                          e.currentTarget.src = `https://picsum.photos/seed/${similarProduct.id}/200/200`;
                        }}
                      />
                    </div>
                  </div>
                  <CardContent className="p-3">
                    <h3 className="font-medium text-sm line-clamp-1">{similarProduct.title}</h3>
                    <div className="flex justify-between items-center mt-1">
                      <p className="text-sm font-semibold">{formatCurrencyDa(similarProduct.price)}</p>
                      <p className="text-xs text-emerald-600">{Math.round(similarProduct.commission)}%</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Copy Link Dialog */}
      <CopyLinkDialog
        open={copyLinkDialogOpen}
        onOpenChange={setCopyLinkDialogOpen}
        productId={product.id}
        productName={productName}
      />

      {/* Add Buyer Dialog */}
      <AddBuyerDialog
        open={addBuyerDialogOpen}
        onOpenChange={setAddBuyerDialogOpen}
        productId={product.id}
        productName={productName}
        productPrice={product.price}
        affiliateEarningPrice={product.affiliate_earning_price}
        availableColors={availableColors}
        availableSizes={availableSizes}
        productImage={productImages[0]}
      />
    </Layout>
  );
};

export default ProductDetail;
