-- Fix affiliate_worker_id foreign key references to use auth.users instead of affiliate_workers
-- Run this script in your Supabase SQL editor to fix the foreign key references

-- Step 1: Drop existing foreign key constraints
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_affiliate_worker_id_fkey;
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_affiliate_worker_id_fkey;

-- Step 2: Update the foreign key constraints to reference auth.users
ALTER TABLE orders
ADD CONSTRAINT orders_affiliate_worker_id_fkey
FOREIGN KEY (affiliate_worker_id) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE payments
ADD CONSTRAINT payments_affiliate_worker_id_fkey
FOREIGN KEY (affiliate_worker_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 3: Update indexes (drop old ones and create new ones if needed)
DROP INDEX IF EXISTS idx_orders_affiliate_worker_id;
DROP INDEX IF EXISTS idx_payments_affiliate_worker_id;

CREATE INDEX IF NOT EXISTS idx_orders_affiliate_worker_id ON orders(affiliate_worker_id);
CREATE INDEX IF NOT EXISTS idx_payments_affiliate_worker_id ON payments(affiliate_worker_id);

-- Step 4: Create a view to easily join orders with user profiles
CREATE OR REPLACE VIEW orders_with_profiles AS
SELECT
    o.*,
    p.name as affiliate_worker_name,
    p.phone as affiliate_worker_phone,
    p.role as affiliate_worker_role
FROM orders o
LEFT JOIN profiles p ON o.affiliate_worker_id = p.id;

-- Step 5: Create a view to easily join payments with user profiles
CREATE OR REPLACE VIEW payments_with_profiles AS
SELECT
    pay.*,
    p.name as affiliate_worker_name,
    p.phone as affiliate_worker_phone,
    p.role as affiliate_worker_role
FROM payments pay
LEFT JOIN profiles p ON pay.affiliate_worker_id = p.id;

-- Step 6: Add comments for clarity
COMMENT ON COLUMN orders.affiliate_worker_id IS 'References auth.users(id) - the user who created this order';
COMMENT ON COLUMN payments.affiliate_worker_id IS 'References auth.users(id) - the user receiving this payment';
COMMENT ON VIEW orders_with_profiles IS 'Orders joined with user profiles for easy querying';
COMMENT ON VIEW payments_with_profiles IS 'Payments joined with user profiles for easy querying';



