-- Remove email column from customers table
-- Run this script in your Supabase SQL editor to remove the email column

-- Step 1: Drop the unique constraint on email (if it exists)
ALTER TABLE customers DROP CONSTRAINT IF EXISTS customers_email_key;

-- Step 2: Drop the email column
ALTER TABLE customers DROP COLUMN IF EXISTS email;

-- Step 3: Update any indexes that might reference the email column
-- (Check if there are any and drop them)
DROP INDEX IF EXISTS idx_customers_email;

-- Step 4: Add comment to document the change
COMMENT ON TABLE customers IS 'Customer information without email - phone is the primary contact method';

-- Verify the table structure after changes
-- You can run this to see the updated table structure:
-- \d customers;


