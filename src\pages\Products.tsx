
import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import ProductCard from '@/components/dashboard/ProductCard';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, SlidersHorizontal, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { getEnhancedProducts, getEnhancedProductsByCategory } from '@/lib/api';

const Products = () => {
  const location = useLocation();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('default');
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);

  // Fetch products based on category filter
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams(location.search);
        const category = params.get('category');

        if (category) {
          setCategoryFilter(category);
          const categoryProducts = await getEnhancedProductsByCategory(category);
          setProducts(categoryProducts);
        } else {
          const allProducts = await getEnhancedProducts();
          setProducts(allProducts);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [location.search]);

  // Filter products based on search term
  const filteredProducts = products.filter(product => {
    // First filter by search term
    const matchesSearch =
      product.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Sort products based on selected option
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-asc':
        return a.price - b.price;
      case 'price-desc':
        return b.price - a.price;
      case 'commission-desc':
        return b.commission - a.commission;
      case 'rating-high-low':
        return b.rating.rate - a.rating.rate;
      default:
        return 0;
    }
  });

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">Products</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Browse available products to promote
        </p>

        {categoryFilter && (
          <div className="mt-2 flex items-center gap-2">
            <span className="text-sm">Filtered by:</span>
            <Badge className="flex items-center gap-1.5 px-3 py-1">
              <Tag size={14} />
              {categoryFilter.charAt(0).toUpperCase() + categoryFilter.slice(1)}
              <button
                className="ml-1 hover:text-primary"
                onClick={() => {
                  setCategoryFilter(null);
                  window.history.pushState({}, '', '/products');
                  window.location.reload();
                }}
                aria-label="Remove filter"
              >
                ×
              </button>
            </Badge>
          </div>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search products..."
            className="pl-10 text-sm sm:text-base"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto sm:min-w-[200px]">
          <SlidersHorizontal className="h-4 w-4 text-muted-foreground" />
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="text-sm sm:text-base flex-1 sm:flex-none">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">Default</SelectItem>
              <SelectItem value="price-asc">Price: Low to High</SelectItem>
              <SelectItem value="price-desc">Price: High to Low</SelectItem>
              <SelectItem value="commission-desc">Highest Commission</SelectItem>
              <SelectItem value="rating-high-low">Highest Rating</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {loading ? (
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="h-[300px] animate-pulse">
              <div className="h-full flex flex-col">
                <div className="h-[180px] bg-muted rounded-t-lg"></div>
                <div className="p-4 flex-1 flex flex-col gap-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="mt-auto h-8 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
        </div>
      ) : sortedProducts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">No products found matching your search.</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-5 md:gap-6">
          {sortedProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      )}
    </Layout>
  );
};

export default Products;
