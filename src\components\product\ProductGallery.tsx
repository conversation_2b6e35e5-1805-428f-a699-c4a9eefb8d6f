import { useState } from "react";
import { cn } from "@/lib/utils";

interface ProductGalleryProps {
  images: string[];
  productName: string;
}

const ProductGallery = ({ images, productName }: ProductGalleryProps) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [imageLoaded, setImageLoaded] = useState<boolean[]>(Array(images.length).fill(false));

  // Fallback image in case the main one fails
  const fallbackImage = `https://picsum.photos/seed/${productName.replace(/\s+/g, '-')}/800/600`;

  const handleImageLoad = (index: number) => {
    const newLoadedState = [...imageLoaded];
    newLoadedState[index] = true;
    setImageLoaded(newLoadedState);
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallbackImage;
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Main image */}
      <div className="relative rounded-lg overflow-hidden aspect-video md:aspect-square">
        {!imageLoaded[selectedImage] && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-10 h-10 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        <img
          src={images[selectedImage]}
          alt={`${productName} - Image ${selectedImage + 1}`}
          className={cn(
            "w-full h-full object-contain transition-opacity duration-300 rounded-md",
            imageLoaded[selectedImage] ? "opacity-100" : "opacity-0"
          )}
          onLoad={() => handleImageLoad(selectedImage)}
          onError={handleImageError}
        />
      </div>

      {/* Thumbnails */}
      {images.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <button
              key={index}
              className={cn(
                "relative rounded-md overflow-hidden border-2 h-16 w-16 sm:h-20 sm:w-20 flex-shrink-0",
                selectedImage === index ? "border-black" : "border-transparent hover:border-gray-300"
              )}
              onClick={() => setSelectedImage(index)}
              aria-label={`View image ${index + 1}`}
            >
              <img
                src={image}
                alt={`${productName} - Thumbnail ${index + 1}`}
                className="w-full h-full object-cover rounded-sm"
                onError={handleImageError}
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductGallery;
